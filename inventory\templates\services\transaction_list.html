{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary mb-0">
                    <i class="fas fa-list me-2"></i>{{ page_title }}
                </h2>
                <a href="{% url 'service_transaction_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>New Transaction
                </a>
            </div>
            <p class="text-muted mb-0">Manage service transactions and customer packages</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Transactions</h6>
                            <h3 class="mb-0">{{ stats.total_transactions }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-receipt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Active Sessions</h6>
                            <h3 class="mb-0">{{ stats.active_sessions }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-play-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Completed Sessions</h6>
                            <h3 class="mb-0">{{ stats.completed_sessions }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Revenue</h6>
                            <h3 class="mb-0">₱{{ stats.total_revenue|floatformat:2|intcomma }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-peso-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Search Customer</label>
                            <input type="text" name="customer" class="form-control" 
                                   value="{{ request.GET.customer }}" placeholder="Customer name...">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-select">
                                <option value="">All Status</option>
                                <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                                <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>Completed</option>
                                <option value="expired" {% if request.GET.status == 'expired' %}selected{% endif %}>Expired</option>
                                <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Service</label>
                            <select name="service" class="form-select">
                                <option value="">All Services</option>
                                <!-- Services will be populated by the view -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Transactions Table -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    {% if page_obj %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Transaction ID</th>
                                    <th>Customer</th>
                                    <th>Service</th>
                                    <th>Sessions</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Payment Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for line_item in page_obj %}
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ line_item.transaction.transaction_id }}</strong>
                                    </td>
                                    <td>
                                        <div>{{ line_item.transaction.customer_name }}</div>
                                        {% if line_item.transaction.customer_phone %}
                                        <small class="text-muted">{{ line_item.transaction.customer_phone }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>{{ line_item.service.name }}</div>
                                        <small class="text-muted">{{ line_item.service.get_category_display }}</small>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                                {% widthratio line_item.sessions_used line_item.sessions 100 as completion_percentage %}
                                                <div class="progress-bar" role="progressbar"
                                                     style="width: {{ completion_percentage }}%"
                                                     aria-valuenow="{{ completion_percentage }}"
                                                     aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                            <small class="text-muted">{{ line_item.sessions_used }}/{{ line_item.sessions }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>₱{{ line_item.total_price|floatformat:2|intcomma }}</strong>
                                    </td>
                                    <td>
                                        {% if line_item.sessions_used >= line_item.sessions %}
                                            <span class="badge bg-primary">Completed</span>
                                        {% elif line_item.sessions_used > 0 %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-warning">Unused</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ line_item.transaction.payment_date|date:"M d, Y" }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'customer_session_detail' line_item.pk %}"
                                               class="btn btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if line_item.sessions_remaining > 0 %}
                                            <a href="{% url 'customer_session_detail' line_item.pk %}"
                                               class="btn btn-outline-success" title="Manage Sessions">
                                                <i class="fas fa-calendar"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Transaction pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                            </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                            {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Transactions Found</h5>
                        <p class="text-muted">Start by creating your first service transaction.</p>
                        <a href="{% url 'service_transaction_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create First Transaction
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
