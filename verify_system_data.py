#!/usr/bin/env python3
"""
Verify that the system has comprehensive test data
Run with: python manage.py shell < verify_system_data.py
"""

from inventory.models import Product, Service
from inventory.models_unified import UnifiedTransaction, UnifiedTransactionLineItem
from inventory.models_session import SessionConsumption, SessionAppointment

def verify_system_data():
    """Verify all system components have data"""
    print("🔍 SYSTEM DATA VERIFICATION")
    print("=" * 50)
    
    # Products
    products = Product.objects.all()
    products_with_stock = Product.objects.filter(current_stock__gt=0)
    print(f"📦 PRODUCTS:")
    print(f"   Total products: {products.count()}")
    print(f"   Products with stock: {products_with_stock.count()}")
    print(f"   Total stock value: {sum(p.current_stock for p in products_with_stock)}")
    
    # Services
    services = Service.objects.filter(is_active=True)
    print(f"\n💆 SERVICES:")
    print(f"   Active services: {services.count()}")
    
    # Transactions
    transactions = UnifiedTransaction.objects.all()
    product_transactions = transactions.filter(line_items__item_type='product').distinct()
    service_transactions = transactions.filter(line_items__item_type='service').distinct()
    mixed_transactions = transactions.filter(
        line_items__item_type='product'
    ).filter(
        line_items__item_type='service'
    ).distinct()
    
    print(f"\n💳 TRANSACTIONS:")
    print(f"   Total transactions: {transactions.count()}")
    print(f"   Product-only transactions: {product_transactions.count()}")
    print(f"   Service-only transactions: {service_transactions.count()}")
    print(f"   Mixed transactions: {mixed_transactions.count()}")
    
    # Payment methods
    cash_transactions = transactions.filter(payment_mode='cash')
    gcash_transactions = transactions.filter(payment_mode='gcash')
    print(f"   Cash payments: {cash_transactions.count()}")
    print(f"   GCash payments: {gcash_transactions.count()}")
    
    # Discounts
    discounted_transactions = transactions.filter(discount_percent__gt=0)
    print(f"   Transactions with discounts: {discounted_transactions.count()}")
    
    # Line Items
    line_items = UnifiedTransactionLineItem.objects.all()
    product_line_items = line_items.filter(item_type='product')
    service_line_items = line_items.filter(item_type='service')
    
    print(f"\n📋 LINE ITEMS:")
    print(f"   Total line items: {line_items.count()}")
    print(f"   Product line items: {product_line_items.count()}")
    print(f"   Service line items: {service_line_items.count()}")
    
    # Sessions
    session_consumptions = SessionConsumption.objects.all()
    session_appointments = SessionAppointment.objects.all()
    
    print(f"\n🗓️ SESSIONS:")
    print(f"   Session consumptions: {session_consumptions.count()}")
    print(f"   Scheduled appointments: {session_appointments.count()}")
    
    # Session states
    service_line_items_with_sessions = service_line_items.filter(sessions__gt=0)
    completed_services = service_line_items.filter(sessions_used__gte=models.F('sessions'))
    partial_services = service_line_items.filter(
        sessions_used__gt=0,
        sessions_used__lt=models.F('sessions')
    )
    unused_services = service_line_items.filter(sessions_used=0)
    
    print(f"   Services with sessions: {service_line_items_with_sessions.count()}")
    print(f"   Fully completed services: {completed_services.count()}")
    print(f"   Partially used services: {partial_services.count()}")
    print(f"   Unused services (paid but not started): {unused_services.count()}")
    
    # Customer data
    unique_customers = transactions.values('customer_name').distinct().count()
    print(f"\n👥 CUSTOMERS:")
    print(f"   Unique customers: {unique_customers}")
    
    # Sample data examples
    print(f"\n📊 SAMPLE DATA:")
    
    # Show some transactions
    print("   Recent transactions:")
    for transaction in transactions.order_by('-payment_date')[:3]:
        print(f"     • {transaction.customer_name} - {transaction.payment_mode} - ₱{transaction.final_total}")
    
    # Show some sessions
    print("   Recent session consumptions:")
    for session in session_consumptions.order_by('-session_date')[:3]:
        print(f"     • {session.customer_name} - {session.service_name} - Session {session.session_number}")
    
    # Show some appointments
    print("   Upcoming appointments:")
    for appointment in session_appointments.filter(
        appointment_date__gte=timezone.now()
    ).order_by('appointment_date')[:3]:
        print(f"     • {appointment.customer_name} - {appointment.service_name} - {appointment.appointment_date.strftime('%Y-%m-%d %H:%M')}")
    
    print(f"\n✅ VERIFICATION COMPLETE")
    print("=" * 50)
    
    # Summary
    has_products = products_with_stock.count() > 0
    has_transactions = transactions.count() > 0
    has_sessions = session_consumptions.count() > 0
    has_appointments = session_appointments.count() > 0
    
    if all([has_products, has_transactions, has_sessions, has_appointments]):
        print("🎉 SUCCESS: System has comprehensive test data!")
        print("   ✓ Products with stock")
        print("   ✓ Various transaction types")
        print("   ✓ Session consumption data")
        print("   ✓ Scheduled appointments")
        print("\n🚀 The system is ready for real-world testing!")
    else:
        print("⚠️  WARNING: Some data is missing:")
        if not has_products:
            print("   ✗ No products with stock")
        if not has_transactions:
            print("   ✗ No transactions")
        if not has_sessions:
            print("   ✗ No session consumptions")
        if not has_appointments:
            print("   ✗ No scheduled appointments")

if __name__ == "__main__":
    from django.db import models
    from django.utils import timezone
    
    verify_system_data()
