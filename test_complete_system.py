#!/usr/bin/env python3
"""
Comprehensive system test for REMR Aesthetic Centre POS
Run with: python manage.py shell < test_complete_system.py
"""

from inventory.models import Service, Product
from inventory.models_unified import UnifiedTransaction, UnifiedTransactionLineItem, DiscountVoucher
from decimal import Decimal
from django.utils import timezone

def test_data_integrity():
    """Test that all data from data.py is properly imported"""
    print("=== TESTING DATA INTEGRITY ===")
    
    # Check service counts by category
    service_categories = {
        'facial_treatment': 0,
        'gluta_treatment': 0,
        'ipl_treatment': 0,
        'aesthetic_procedure': 0,
        'other': 0
    }
    
    for service in Service.objects.all():
        cat = service.category
        if cat in service_categories:
            service_categories[cat] += 1
        else:
            service_categories['other'] += 1
    
    print("Service Categories:")
    for cat, count in service_categories.items():
        print(f"  {cat}: {count} services")
    
    # Check products
    product_count = Product.objects.count()
    print(f"\nProducts: {product_count} total")
    
    # Check key services from data.py
    key_services = [
        "Deep Cleansing facial",
        "Classic skin white IV Push",
        "Acne control laser",
        "Botox forehead",
        "HIFU"
    ]
    
    print("\nKey Services Check:")
    for service_name in key_services:
        exists = Service.objects.filter(name__icontains=service_name.split()[0]).exists()
        print(f"  {service_name}: {'✓' if exists else '✗'}")
    
    return service_categories, product_count

def test_voucher_system():
    """Test voucher functionality"""
    print("\n=== TESTING VOUCHER SYSTEM ===")
    
    # Check vouchers
    voucher_count = DiscountVoucher.objects.count()
    print(f"Total vouchers: {voucher_count}")
    
    # Test voucher validation
    test_voucher = DiscountVoucher.objects.filter(code='FACIAL10').first()
    if test_voucher:
        print(f"Test voucher: {test_voucher.code} - {test_voucher.name}")
        print(f"  Valid: {test_voucher.is_valid}")
        print(f"  Discount for ₱1000: ₱{test_voucher.calculate_discount(Decimal('1000.00'))}")
    else:
        print("No test voucher found")
    
    return voucher_count

def test_promotional_pricing():
    """Test promotional pricing features"""
    print("\n=== TESTING PROMOTIONAL PRICING ===")
    
    # Create a test service with promotional pricing
    test_service = Service.objects.filter(name__icontains='facial').first()
    if test_service:
        # Set promotional pricing
        test_service.promotional_price = Decimal('399.00')
        test_service.cash_discount_price = Decimal('350.00')
        test_service.is_on_promotion = True
        test_service.promotion_start_date = timezone.now()
        test_service.save()
        
        print(f"Test Service: {test_service.name}")
        print(f"  Regular Price: ₱{test_service.price_per_session}")
        print(f"  Promotional Price: ₱{test_service.promotional_price}")
        print(f"  Cash Price: ₱{test_service.cash_discount_price}")
        print(f"  Effective Price: ₱{test_service.effective_price}")
        print(f"  Cash Price: ₱{test_service.cash_price}")
        print(f"  Is Promotion Active: {test_service.is_promotion_active}")
    
    # Test product promotional pricing
    test_product = Product.objects.first()
    if test_product:
        test_product.promotional_price = Decimal('199.00')
        test_product.cash_discount_price = Decimal('180.00')
        test_product.is_on_promotion = True
        test_product.promotion_start_date = timezone.now()
        test_product.save()
        
        print(f"\nTest Product: {test_product.name}")
        print(f"  Regular Price: ₱{test_product.selling_price}")
        print(f"  Promotional Price: ₱{test_product.promotional_price}")
        print(f"  Cash Price: ₱{test_product.cash_discount_price}")
        print(f"  Effective Price: ₱{test_product.effective_price}")
        print(f"  Cash Price: ₱{test_product.cash_price}")
        print(f"  Is Promotion Active: {test_product.is_promotion_active}")

def test_transaction_creation():
    """Test transaction creation with mixed items"""
    print("\n=== TESTING TRANSACTION CREATION ===")
    
    try:
        # Get test items
        test_service = Service.objects.first()
        test_product = Product.objects.filter(current_stock__gt=0).first()
        
        if not test_service or not test_product:
            print("❌ No test items available")
            return False
        
        # Create transaction
        transaction = UnifiedTransaction.objects.create(
            customer_name='Test Customer',
            customer_phone='09123456789',
            payment_mode='cash',
            payment_status='paid',
            cash_amount_received=Decimal('2000.00'),
            subtotal=Decimal('0.00'),
            discount_percent=5,
            voucher_codes_used='FACIAL10',
            voucher_discount=Decimal('50.00'),
            payment_date=timezone.now()
        )
        
        # Add service line item
        service_line = UnifiedTransactionLineItem.objects.create(
            transaction=transaction,
            item_type='service',
            service=test_service,
            sessions=1,
            unit_price=test_service.effective_price
        )
        
        # Add product line item
        product_line = UnifiedTransactionLineItem.objects.create(
            transaction=transaction,
            item_type='product',
            product=test_product,
            quantity=1,
            unit_price=test_product.effective_price
        )
        
        # Refresh transaction to see calculated totals
        transaction.refresh_from_db()
        
        print(f"Transaction created: {transaction.transaction_id}")
        print(f"  Subtotal: P{transaction.subtotal}")
        print(f"  Manual Discount: {transaction.discount_percent}%")
        print(f"  Voucher Discount: P{transaction.voucher_discount}")
        print(f"  Total Discount: P{transaction.discount_amount}")
        print(f"  Final Total: P{transaction.total_amount}")
        print(f"  Change: P{transaction.change_amount}")
        
        # Check line items
        print(f"  Line Items: {transaction.line_items.count()}")
        for item in transaction.line_items.all():
            print(f"    - {item.item_type}: {item.item_name} x{item.quantity or item.sessions} = P{item.line_total}")
        
        return True
        
    except Exception as e:
        print(f"❌ Transaction creation failed: {e}")
        return False

def test_inventory_tracking():
    """Test inventory tracking"""
    print("\n=== TESTING INVENTORY TRACKING ===")
    
    # Check product stock levels
    low_stock_products = Product.objects.filter(current_stock__lte=5).count()
    total_products = Product.objects.count()
    
    print(f"Products with low stock (≤5): {low_stock_products}/{total_products}")
    
    # Check service session tracking
    active_service_transactions = UnifiedTransaction.objects.filter(
        payment_status='paid',
        line_items__item_type='service'
    ).count()
    
    print(f"Transactions with services: {active_service_transactions}")
    
    return low_stock_products, active_service_transactions

def run_complete_test():
    """Run all tests"""
    print("REMR AESTHETIC CENTRE POS SYSTEM TEST")
    print("=" * 50)
    
    # Test 1: Data Integrity
    service_cats, product_count = test_data_integrity()
    
    # Test 2: Voucher System
    voucher_count = test_voucher_system()
    
    # Test 3: Promotional Pricing
    test_promotional_pricing()
    
    # Test 4: Transaction Creation
    transaction_success = test_transaction_creation()
    
    # Test 5: Inventory Tracking
    low_stock, service_transactions = test_inventory_tracking()
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    total_services = sum(service_cats.values())
    print(f"✓ Services imported: {total_services}")
    print(f"✓ Products imported: {product_count}")
    print(f"✓ Vouchers created: {voucher_count}")
    print(f"Transaction creation: {'PASS' if transaction_success else 'FAIL'}")
    print(f"Inventory tracking: {low_stock} low stock items")
    print(f"Service transactions: {service_transactions}")

    # Overall status
    all_tests_pass = (
        total_services > 70 and  # Should have most services
        product_count > 15 and   # Should have products
        voucher_count > 0 and    # Should have vouchers
        transaction_success      # Transaction should work
    )

    print(f"\nOVERALL STATUS: {'SYSTEM READY' if all_tests_pass else 'NEEDS ATTENTION'}")

    if all_tests_pass:
        print("\nThe REMR Aesthetic Centre POS system is fully operational!")
        print("All major components are working correctly.")
    else:
        print("\nSome issues detected. Please review the test results above.")

if __name__ == "__main__":
    run_complete_test()
