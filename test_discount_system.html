<!DOCTYPE html>
<html>
<head>
    <title>Test Discount System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; }
        .discount-list { margin-top: 10px; }
        .discount-item { background: #fff3cd; padding: 8px; margin: 5px 0; border-radius: 4px; display: flex; justify-content: space-between; }
        .remove-btn { background: #dc3545; color: white; border: none; padding: 2px 8px; border-radius: 3px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Discount System Test</h1>
        
        <div class="form-group">
            <label>Subtotal:</label>
            <input type="number" id="subtotal" value="1000" step="0.01">
        </div>
        
        <div class="form-group">
            <label>Discount Type:</label>
            <select id="discount-type">
                <option value="percentage">Percentage Discount</option>
                <option value="fixed">Fixed Amount Discount</option>
                <option value="promotional">Promotional Price</option>
                <option value="cash">Cash Discount</option>
                <option value="loyalty">Loyalty Discount</option>
                <option value="senior">Senior/PWD Discount</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Discount Value:</label>
            <div style="display: flex;">
                <span id="discount-symbol" style="padding: 8px; background: #e9ecef; border: 1px solid #ddd; border-right: none; border-radius: 4px 0 0 4px;">%</span>
                <input type="number" id="discount-value" min="0" step="0.1" style="border-radius: 0 4px 4px 0;">
            </div>
            <small id="discount-help">Enter percentage (0-100)</small>
        </div>
        
        <div class="form-group">
            <label>Reason/Notes:</label>
            <input type="text" id="discount-reason" placeholder="e.g., New customer, Loyalty reward">
        </div>
        
        <button onclick="addDiscount()">Add Discount</button>
        
        <div class="result">
            <h3>Applied Discounts:</h3>
            <div id="discount-list" class="discount-list"></div>
            
            <h3>Summary:</h3>
            <p>Subtotal: ₱<span id="display-subtotal">1000.00</span></p>
            <p>Total Discount: ₱<span id="total-discount">0.00</span></p>
            <p><strong>Final Total: ₱<span id="final-total">1000.00</span></strong></p>
        </div>
    </div>

    <script>
        let appliedDiscounts = [];
        let totalDiscountAmount = 0;

        function updateDiscountUI() {
            const discountType = document.getElementById('discount-type').value;
            const discountSymbol = document.getElementById('discount-symbol');
            const discountHelp = document.getElementById('discount-help');
            const discountValue = document.getElementById('discount-value');
            
            switch(discountType) {
                case 'percentage':
                    discountSymbol.textContent = '%';
                    discountHelp.textContent = 'Enter percentage (0-100)';
                    discountValue.max = '100';
                    break;
                case 'fixed':
                    discountSymbol.textContent = '₱';
                    discountHelp.textContent = 'Enter fixed amount';
                    discountValue.max = '';
                    break;
                case 'promotional':
                    discountSymbol.textContent = '₱';
                    discountHelp.textContent = 'Enter promotional price';
                    discountValue.max = '';
                    break;
                case 'cash':
                    discountSymbol.textContent = '%';
                    discountHelp.textContent = 'Cash discount percentage (0-20)';
                    discountValue.max = '20';
                    break;
                case 'loyalty':
                    discountSymbol.textContent = '%';
                    discountHelp.textContent = 'Loyalty discount percentage (0-15)';
                    discountValue.max = '15';
                    break;
                case 'senior':
                    discountSymbol.textContent = '%';
                    discountHelp.textContent = 'Senior/PWD discount (20%)';
                    discountValue.value = '20';
                    discountValue.max = '20';
                    break;
            }
        }

        function addDiscount() {
            const discountType = document.getElementById('discount-type').value;
            const discountValue = parseFloat(document.getElementById('discount-value').value) || 0;
            const discountReason = document.getElementById('discount-reason').value.trim();
            const subtotal = parseFloat(document.getElementById('subtotal').value) || 0;
            
            if (discountValue <= 0) {
                alert('Please enter a valid discount value');
                return;
            }
            
            // Calculate discount amount
            let discountAmount = 0;
            
            switch(discountType) {
                case 'percentage':
                case 'cash':
                case 'loyalty':
                case 'senior':
                    discountAmount = subtotal * (discountValue / 100);
                    break;
                case 'fixed':
                    discountAmount = Math.min(discountValue, subtotal);
                    break;
                case 'promotional':
                    discountAmount = Math.max(0, subtotal - discountValue);
                    break;
            }
            
            // Add to applied discounts
            const discount = {
                id: Date.now(),
                type: discountType,
                value: discountValue,
                amount: discountAmount,
                reason: discountReason || getDiscountTypeName(discountType)
            };
            
            appliedDiscounts.push(discount);
            updateDisplay();
            
            // Clear inputs (but don't reset to 0 for senior discount)
            if (discountType !== 'senior') {
                document.getElementById('discount-value').value = '';
            }
            document.getElementById('discount-reason').value = '';
        }

        function removeDiscount(discountId) {
            appliedDiscounts = appliedDiscounts.filter(d => d.id !== discountId);
            updateDisplay();
        }

        function getDiscountTypeName(type) {
            const names = {
                'percentage': 'Percentage Discount',
                'fixed': 'Fixed Amount Discount',
                'promotional': 'Promotional Price',
                'cash': 'Cash Discount',
                'loyalty': 'Loyalty Discount',
                'senior': 'Senior/PWD Discount'
            };
            return names[type] || type;
        }

        function updateDisplay() {
            const subtotal = parseFloat(document.getElementById('subtotal').value) || 0;
            totalDiscountAmount = appliedDiscounts.reduce((total, d) => total + d.amount, 0);
            const finalTotal = subtotal - totalDiscountAmount;
            
            // Update summary
            document.getElementById('display-subtotal').textContent = subtotal.toFixed(2);
            document.getElementById('total-discount').textContent = totalDiscountAmount.toFixed(2);
            document.getElementById('final-total').textContent = finalTotal.toFixed(2);
            
            // Update discount list
            const discountList = document.getElementById('discount-list');
            discountList.innerHTML = '';
            
            appliedDiscounts.forEach(discount => {
                const discountItem = document.createElement('div');
                discountItem.className = 'discount-item';
                discountItem.innerHTML = `
                    <span>${discount.reason} - ₱${discount.amount.toFixed(2)}</span>
                    <button class="remove-btn" onclick="removeDiscount(${discount.id})">Remove</button>
                `;
                discountList.appendChild(discountItem);
            });
        }

        // Event listeners
        document.getElementById('discount-type').addEventListener('change', updateDiscountUI);
        document.getElementById('subtotal').addEventListener('input', updateDisplay);

        // Initialize
        updateDiscountUI();
        updateDisplay();
    </script>
</body>
</html>
