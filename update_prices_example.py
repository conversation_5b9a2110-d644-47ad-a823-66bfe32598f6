#!/usr/bin/env python3
"""
Example script to update prices programmatically
Run this with: python manage.py shell < update_prices_example.py
"""

from inventory.models import Service, Product
from decimal import Decimal

# Example: Update service prices
def update_service_prices():
    """Update service prices by category or individual services"""
    
    # Update all facial services by 10%
    facial_services = Service.objects.filter(category='facial_treatment')
    for service in facial_services:
        old_price = service.price_per_session
        new_price = old_price * Decimal('1.10')  # 10% increase
        service.price_per_session = new_price
        service.save()
        print(f"Updated {service.name}: ₱{old_price} → ₱{new_price}")
    
    # Update specific service by name
    try:
        specific_service = Service.objects.get(name__icontains="Deep Cleansing")
        specific_service.price_per_session = Decimal('599.00')
        specific_service.save()
        print(f"Updated {specific_service.name} to ₱{specific_service.price_per_session}")
    except Service.DoesNotExist:
        print("Service not found")

def update_product_prices():
    """Update product prices by category or individual products"""
    
    # Update all skincare products by 5%
    skincare_products = Product.objects.filter(category='skincare')
    for product in skincare_products:
        old_price = product.selling_price
        new_price = old_price * Decimal('1.05')  # 5% increase
        product.selling_price = new_price
        product.save()
        print(f"Updated {product.name}: ₱{old_price} → ₱{new_price}")
    
    # Update specific product by SKU
    try:
        specific_product = Product.objects.get(sku="PROD0001")
        specific_product.selling_price = Decimal('1299.00')
        specific_product.cost_price = Decimal('800.00')
        specific_product.save()
        print(f"Updated {specific_product.name} - Selling: ₱{specific_product.selling_price}, Cost: ₱{specific_product.cost_price}")
    except Product.DoesNotExist:
        print("Product not found")

def bulk_price_update_by_percentage():
    """Apply percentage increase to all items"""
    
    # 5% increase on all services
    services = Service.objects.all()
    for service in services:
        service.price_per_session = service.price_per_session * Decimal('1.05')
        service.save()
    
    # 3% increase on all products
    products = Product.objects.all()
    for product in products:
        product.selling_price = product.selling_price * Decimal('1.03')
        product.save()
    
    print("Bulk price update completed")

# Example usage:
if __name__ == "__main__":
    print("Price update examples:")
    print("1. update_service_prices() - Update service prices")
    print("2. update_product_prices() - Update product prices") 
    print("3. bulk_price_update_by_percentage() - Bulk percentage update")
    print("\nUncomment the function you want to run:")
    
    # Uncomment the function you want to execute:
    # update_service_prices()
    # update_product_prices()
    # bulk_price_update_by_percentage()
