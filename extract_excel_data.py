#!/usr/bin/env python3
"""
Script to extract product data from PRODUCT-FOR-POS.xlsx and prepare it for import
into the REMR Aesthetic Centre POS system.
"""

import pandas as pd
import json
import sys
import os

def parse_service_from_sheet(df, sheet_name):
    """Parse service/product data from a specific sheet format"""
    services = []
    current_service = {}

    for index, row in df.iterrows():
        # Convert row to string values, handling NaN
        row_values = [str(val).strip() if pd.notna(val) else '' for val in row]

        # Skip completely empty rows
        if all(val == '' or val == 'nan' for val in row_values):
            continue

        first_col = row_values[0].lower()

        # Check if this is a product name row
        if 'product name:' in first_col:
            # Save previous service if it exists
            if current_service.get('name'):
                services.append(current_service.copy())

            # Start new service
            current_service = {
                'name': row_values[1] if len(row_values) > 1 and row_values[1] else 'Unknown Service',
                'category': sheet_name.lower().replace(' ', '_'),
                'description': '',
                'selling_price': 0.0,
                'cost_price': 0.0,
                'current_stock': 0,
                'inventory_items': []
            }

        elif 'ala carte price:' in first_col:
            if current_service:
                price_str = row_values[1] if len(row_values) > 1 else ''
                try:
                    # Extract numeric value from price string
                    price_clean = price_str.replace('P', '').replace('₱', '').replace(',', '').strip()
                    current_service['selling_price'] = float(price_clean) if price_clean else 0.0
                except (ValueError, AttributeError):
                    current_service['selling_price'] = 0.0

        elif 'product category:' in first_col:
            if current_service and len(row_values) > 1 and row_values[1]:
                current_service['category'] = row_values[1].lower().replace(' ', '_')

        elif 'product pack to be deducted in inventory:' in first_col:
            if current_service and len(row_values) > 1 and row_values[1]:
                current_service['inventory_items'].append(row_values[1])
                current_service['description'] = f"Includes: {row_values[1]}"

        # Handle cases where the service name is in the column header (like OPT sheet)
        elif index == 0 and len(row_values) > 1 and row_values[1] and 'unnamed' not in row_values[1].lower():
            # This might be a service name in the header
            if not current_service.get('name') or current_service['name'] == 'Unknown Service':
                current_service = {
                    'name': row_values[1],
                    'category': sheet_name.lower().replace(' ', '_'),
                    'description': '',
                    'selling_price': 0.0,
                    'cost_price': 0.0,
                    'current_stock': 0,
                    'inventory_items': []
                }

    # Don't forget the last service
    if current_service.get('name'):
        services.append(current_service)

    return services

def extract_excel_data(excel_file_path):
    """Extract product data from Excel file"""
    try:
        # Read all sheets from the Excel file
        excel_data = pd.read_excel(excel_file_path, sheet_name=None)

        print(f"Found {len(excel_data)} sheets in the Excel file:")
        for sheet_name in excel_data.keys():
            print(f"  - {sheet_name}")

        all_services = []

        # Process each sheet
        for sheet_name, df in excel_data.items():
            print(f"\nProcessing sheet: {sheet_name}")
            print(f"Shape: {df.shape}")

            # Skip empty sheets
            if df.empty:
                print(f"Sheet {sheet_name} is empty, skipping...")
                continue

            # Parse services from this sheet
            sheet_services = parse_service_from_sheet(df, sheet_name)

            # Add sheet source and generate SKUs
            for i, service in enumerate(sheet_services):
                service['sheet_source'] = sheet_name
                service['sku'] = f"{sheet_name[:3].upper()}{i+1:03d}"

                # Set default values
                service.setdefault('unit', 'session')
                service.setdefault('minimum_stock', 0)  # Services don't need stock
                service.setdefault('maximum_stock', 999)
                service.setdefault('is_active', True)

                # Map category to our system categories
                category_mapping = {
                    'facial': 'facial_treatment',
                    'gluta': 'gluta_treatment',
                    'meso': 'aesthetic_procedure',
                    'opt': 'ipl_treatment',
                    'rf': 'aesthetic_procedure',
                    'hifuwarts': 'aesthetic_procedure',
                    'take_home_meds': 'skincare',
                    'carbon_laser': 'aesthetic_procedure',
                    'invasive': 'aesthetic_procedure',
                    'packages_5+1_&_10+3': 'other'
                }

                service['category'] = category_mapping.get(service['category'], 'other')

                all_services.append(service)

            print(f"Extracted {len(sheet_services)} services from {sheet_name}")

        return all_services

    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return []

def main():
    excel_file = "PRODUCT-FOR-POS.xlsx"

    if not os.path.exists(excel_file):
        print(f"Excel file '{excel_file}' not found!")
        return

    print(f"Extracting data from {excel_file}...")
    services = extract_excel_data(excel_file)

    if services:
        print(f"\nExtracted {len(services)} services/products:")

        # Save to JSON for review
        with open('extracted_services.json', 'w', encoding='utf-8') as f:
            json.dump(services, f, indent=2, ensure_ascii=False)

        # Display summary
        categories = {}
        sheets = {}
        for service in services:
            cat = service.get('category', 'unknown')
            sheet = service.get('sheet_source', 'unknown')
            categories[cat] = categories.get(cat, 0) + 1
            sheets[sheet] = sheets.get(sheet, 0) + 1

        print("\nService categories:")
        for cat, count in categories.items():
            print(f"  {cat}: {count} services")

        print("\nServices by sheet:")
        for sheet, count in sheets.items():
            print(f"  {sheet}: {count} services")

        # Display first few services
        print("\nFirst 5 services:")
        for i, service in enumerate(services[:5]):
            print(f"\n{i+1}. {service.get('name', 'Unknown')}")
            print(f"   SKU: {service.get('sku', 'N/A')}")
            print(f"   Category: {service.get('category', 'N/A')}")
            print(f"   Price: ₱{service.get('selling_price', 0)}")
            print(f"   Description: {service.get('description', 'N/A')}")
            print(f"   Source: {service.get('sheet_source', 'N/A')}")

        print(f"\nData saved to 'extracted_services.json'")
    else:
        print("No services extracted from the Excel file.")

if __name__ == "__main__":
    main()
