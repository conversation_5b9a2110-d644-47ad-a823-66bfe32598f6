#!/usr/bin/env python3
"""
Create sample vouchers for testing
Run with: python manage.py shell < create_sample_vouchers.py
"""

from inventory.models_unified import DiscountVoucher
from decimal import Decimal
from django.utils import timezone
from datetime import timedelta

def create_sample_vouchers():
    """Create sample vouchers for testing the system"""
    
    print("=== CREATING SAMPLE VOUCHERS ===")
    
    # Get current time for validity
    now = timezone.now()
    future = now + timedelta(days=365)  # Valid for 1 year
    
    vouchers = [
        {
            'code': 'FACIAL10',
            'name': '10% Off Facial Services',
            'voucher_type': 'percentage',
            'discount_percentage': Decimal('10.00'),
            'minimum_amount': Decimal('500.00'),
            'max_uses': 100,
            'applicable_to_services': True,
            'applicable_to_products': False,
            'valid_from': now,
            'valid_until': future,
            'created_by': 'System Admin'
        },
        {
            'code': 'NEWCLIENT20',
            'name': '20% Off for New Clients',
            'voucher_type': 'percentage',
            'discount_percentage': Decimal('20.00'),
            'minimum_amount': Decimal('1000.00'),
            'max_uses': 50,
            'max_uses_per_customer': 1,
            'applicable_to_services': True,
            'applicable_to_products': True,
            'valid_from': now,
            'valid_until': future,
            'created_by': 'System Admin'
        },
        {
            'code': 'CASH100',
            'name': '₱100 Cash Discount',
            'voucher_type': 'fixed_amount',
            'discount_amount': Decimal('100.00'),
            'minimum_amount': Decimal('1500.00'),
            'max_uses': 200,
            'applicable_to_services': True,
            'applicable_to_products': True,
            'valid_from': now,
            'valid_until': future,
            'created_by': 'System Admin'
        },
        {
            'code': 'PROMO999',
            'name': 'Special Promotional Price ₱999',
            'voucher_type': 'promotional',
            'promotional_price': Decimal('999.00'),
            'minimum_amount': Decimal('1200.00'),
            'max_uses': 30,
            'applicable_to_services': True,
            'applicable_to_products': False,
            'valid_from': now,
            'valid_until': future,
            'created_by': 'System Admin'
        },
        {
            'code': 'LOYALTY15',
            'name': '15% Loyalty Discount',
            'voucher_type': 'loyalty',
            'discount_percentage': Decimal('15.00'),
            'minimum_amount': Decimal('800.00'),
            'max_uses': 500,
            'max_uses_per_customer': 5,
            'applicable_to_services': True,
            'applicable_to_products': True,
            'valid_from': now,
            'valid_until': future,
            'created_by': 'System Admin'
        },
        {
            'code': 'BULK25',
            'name': '25% Bulk Purchase Discount',
            'voucher_type': 'bulk',
            'discount_percentage': Decimal('25.00'),
            'minimum_amount': Decimal('3000.00'),
            'max_uses': 20,
            'applicable_to_services': True,
            'applicable_to_products': True,
            'valid_from': now,
            'valid_until': future,
            'created_by': 'System Admin'
        },
        {
            'code': 'SEASONAL50',
            'name': '₱50 Seasonal Discount',
            'voucher_type': 'seasonal',
            'discount_amount': Decimal('50.00'),
            'minimum_amount': Decimal('500.00'),
            'max_uses': 150,
            'applicable_to_services': True,
            'applicable_to_products': True,
            'valid_from': now,
            'valid_until': future,
            'created_by': 'System Admin'
        }
    ]
    
    created_count = 0
    for voucher_data in vouchers:
        voucher, created = DiscountVoucher.objects.get_or_create(
            code=voucher_data['code'],
            defaults=voucher_data
        )
        
        if created:
            print(f"✓ Created: {voucher.code} - {voucher.name}")
            created_count += 1
        else:
            print(f"• Exists: {voucher.code} - {voucher.name}")
    
    print(f"\n=== SUMMARY ===")
    print(f"Created {created_count} new vouchers")
    print(f"Total vouchers in system: {DiscountVoucher.objects.count()}")
    
    print("\n=== VOUCHER CODES FOR TESTING ===")
    for voucher in DiscountVoucher.objects.filter(status='active'):
        discount_info = ""
        if voucher.voucher_type == 'percentage':
            discount_info = f"{voucher.discount_percentage}% off"
        elif voucher.voucher_type == 'fixed_amount':
            discount_info = f"₱{voucher.discount_amount} off"
        elif voucher.voucher_type == 'promotional':
            discount_info = f"Special price ₱{voucher.promotional_price}"
        
        print(f"  {voucher.code}: {discount_info} (min: ₱{voucher.minimum_amount})")

if __name__ == "__main__":
    create_sample_vouchers()
