{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold text-primary mb-0">
                <i class="fas fa-chart-line me-2"></i>{{ page_title }}
            </h2>
            <p class="text-muted mb-0">Comprehensive sales analysis for products and services</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Report Filters</h6>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <label for="item_type" class="form-label">Item Type</label>
                            <select class="form-select" id="item_type" name="item_type">
                                <option value="">All Items</option>
                                <option value="product" {% if item_type_filter == 'product' %}selected{% endif %}>Products Only</option>
                                <option value="service" {% if item_type_filter == 'service' %}selected{% endif %}>Services Only</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-2"></i>Apply Filters
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-4 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Transactions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ summary.total_transactions }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-receipt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₱{{ summary.total_revenue|floatformat:2|intcomma }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-peso-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Average Transaction
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₱{{ summary.average_transaction|floatformat:2|intcomma }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calculator fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Breakdown -->
    <div class="row mb-4">
        <!-- Product Categories -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-success">Product Category Sales</h6>
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="card-body">
                    {% if product_category_breakdown %}
                        {% for category in product_category_breakdown %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="font-weight-bold">{{ category.product__category|title|default:"Other" }}</div>
                                <div class="text-xs text-gray-500">{{ category.total_quantity }} units sold</div>
                            </div>
                            <div class="text-right">
                                <div class="font-weight-bold text-success">₱{{ category.total_revenue|floatformat:2|intcomma }}</div>
                                <div class="text-xs text-gray-500">{{ category.item_count }} product{{ category.item_count|pluralize }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Product Sales</h6>
                            <p class="text-muted small">Product sales will appear here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Service Categories -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Service Category Sales</h6>
                    <i class="fas fa-spa"></i>
                </div>
                <div class="card-body">
                    {% if service_category_breakdown %}
                        {% for category in service_category_breakdown %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="font-weight-bold">{{ category.service__category|title|default:"Other" }}</div>
                                <div class="text-xs text-gray-500">{{ category.total_sessions }} sessions sold</div>
                            </div>
                            <div class="text-right">
                                <div class="font-weight-bold text-primary">₱{{ category.total_revenue|floatformat:2|intcomma }}</div>
                                <div class="text-xs text-gray-500">{{ category.item_count }} service{{ category.item_count|pluralize }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-spa fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Service Sales</h6>
                            <p class="text-muted small">Service sales will appear here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Top Selling Items -->
    <div class="row mb-4">
        <!-- Top Products -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-success">Top Selling Products</h6>
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="card-body">
                    {% if product_sales %}
                        {% for product in product_sales|slice:":10" %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="font-weight-bold">{{ product.product__name }}</div>
                                <div class="text-xs text-gray-500">{{ product.total_quantity }} units • {{ product.transaction_count }} transaction{{ product.transaction_count|pluralize }}</div>
                            </div>
                            <div class="text-right">
                                <div class="font-weight-bold text-success">₱{{ product.total_revenue|floatformat:2|intcomma }}</div>
                                <div class="text-xs text-gray-500">₱{{ product.product__selling_price }}/unit</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Product Sales</h6>
                            <p class="text-muted small">Top products will appear here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top Services -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Top Selling Services</h6>
                    <i class="fas fa-star"></i>
                </div>
                <div class="card-body">
                    {% if service_sales %}
                        {% for service in service_sales|slice:":10" %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="font-weight-bold">{{ service.service__name }}</div>
                                <div class="text-xs text-gray-500">{{ service.total_sessions }} sessions • {{ service.transaction_count }} transaction{{ service.transaction_count|pluralize }}</div>
                            </div>
                            <div class="text-right">
                                <div class="font-weight-bold text-primary">₱{{ service.total_revenue|floatformat:2|intcomma }}</div>
                                <div class="text-xs text-gray-500">₱{{ service.service__price_per_session }}/session</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-spa fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Service Sales</h6>
                            <p class="text-muted small">Top services will appear here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-warning">Payment Methods</h6>
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="card-body">
                    {% if payment_breakdown %}
                        {% for payment in payment_breakdown %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="font-weight-bold">{{ payment.payment_mode|title }}</div>
                                <div class="text-xs text-gray-500">{{ payment.count }} transaction{{ payment.count|pluralize }}</div>
                            </div>
                            <div class="text-right">
                                <div class="font-weight-bold text-warning">₱{{ payment.total_amount|floatformat:2|intcomma }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Payment Data</h6>
                            <p class="text-muted small">Payment breakdown will appear here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-500 {
    color: #858796 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}
