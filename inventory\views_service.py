from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db import models
from django.db.models import Q, Sum, Count, F
from django.core.paginator import Paginator
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
import json

from .models import Service, ServiceTransaction, SessionLog, Customer
from .forms_service import (
    ServiceForm, ServiceTransactionForm, SessionLogForm, ServiceTransactionSearchForm
)


@login_required
def service_list(request):
    """List all services"""
    services = Service.objects.all().order_by('category', 'name')
    
    context = {
        'services': services,
        'page_title': 'Services Management'
    }
    return render(request, 'services/service_list.html', context)


@login_required
def service_create(request):
    """Create a new service"""
    if request.method == 'POST':
        form = ServiceForm(request.POST)
        if form.is_valid():
            service = form.save()
            messages.success(request, f'Service "{service.name}" created successfully!')
            return redirect('service_list')
    else:
        form = ServiceForm()
    
    context = {
        'form': form,
        'page_title': 'Create New Service'
    }
    return render(request, 'services/service_form.html', context)


@login_required
def service_detail(request, pk):
    """View detailed information about a service"""
    service = get_object_or_404(Service, pk=pk)

    # Get related statistics
    total_transactions = ServiceTransaction.objects.filter(service=service).count()
    active_transactions = ServiceTransaction.objects.filter(
        service=service,
        status='active'
    ).count()
    completed_transactions = ServiceTransaction.objects.filter(
        service=service,
        status='completed'
    ).count()
    total_revenue = ServiceTransaction.objects.filter(
        service=service
    ).aggregate(Sum('amount_paid'))['amount_paid__sum'] or 0

    # Get recent transactions
    recent_transactions = ServiceTransaction.objects.filter(
        service=service
    ).order_by('-payment_date')[:5]

    context = {
        'service': service,
        'statistics': {
            'total_transactions': total_transactions,
            'active_transactions': active_transactions,
            'completed_transactions': completed_transactions,
            'total_revenue': total_revenue,
        },
        'recent_transactions': recent_transactions,
        'page_title': f'Service Details: {service.name}'
    }
    return render(request, 'services/service_detail.html', context)


@login_required
def service_edit(request, pk):
    """Edit an existing service"""
    service = get_object_or_404(Service, pk=pk)

    if request.method == 'POST':
        form = ServiceForm(request.POST, instance=service)
        if form.is_valid():
            service = form.save()
            messages.success(request, f'Service "{service.name}" updated successfully!')
            return redirect('service_list')
    else:
        form = ServiceForm(instance=service)

    context = {
        'form': form,
        'service': service,
        'page_title': f'Edit Service: {service.name}'
    }
    return render(request, 'services/service_form.html', context)


@login_required
def service_transaction_list(request):
    """List all service transactions with search and filtering"""
    from .models_unified import UnifiedTransaction, UnifiedTransactionLineItem

    form = ServiceTransactionSearchForm(request.GET)

    # Get service line items from unified transactions
    service_line_items = UnifiedTransactionLineItem.objects.filter(
        item_type='service'
    ).select_related('transaction', 'service')

    # Apply filters
    if form.is_valid():
        search = form.cleaned_data.get('search')
        service = form.cleaned_data.get('service')
        date_from = form.cleaned_data.get('date_from')
        date_to = form.cleaned_data.get('date_to')

        if search:
            service_line_items = service_line_items.filter(
                Q(transaction__transaction_id__icontains=search) |
                Q(transaction__customer_name__icontains=search) |
                Q(service__name__icontains=search)
            )

        if service:
            service_line_items = service_line_items.filter(service=service)

        if date_from:
            service_line_items = service_line_items.filter(transaction__payment_date__date__gte=date_from)

        if date_to:
            service_line_items = service_line_items.filter(transaction__payment_date__date__lte=date_to)

    # Order by payment date (newest first)
    service_line_items = service_line_items.order_by('-transaction__payment_date')

    # Pagination
    paginator = Paginator(service_line_items, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    stats = {
        'total_transactions': service_line_items.values('transaction').distinct().count(),
        'active_sessions': service_line_items.filter(sessions__gt=models.F('sessions_used')).count(),
        'completed_sessions': service_line_items.filter(sessions__lte=models.F('sessions_used')).count(),
        'total_revenue': service_line_items.aggregate(
            total=Sum(models.F('unit_price') * models.F('sessions'))
        )['total'] or 0,
    }
    
    context = {
        'page_obj': page_obj,
        'form': form,
        'stats': stats,
        'page_title': 'Service Transactions'
    }
    return render(request, 'services/transaction_list.html', context)


@login_required
def service_transaction_create(request):
    """Create a new service transaction"""
    if request.method == 'POST':
        form = ServiceTransactionForm(request.POST)
        if form.is_valid():
            transaction = form.save()
            messages.success(
                request, 
                f'Service transaction {transaction.transaction_id} created successfully!'
            )
            return redirect('service_transaction_detail', pk=transaction.pk)
    else:
        form = ServiceTransactionForm()
    
    # Get services for AJAX
    services = Service.objects.filter(is_active=True).values(
        'id', 'name', 'price_per_session', 'duration_minutes'
    )

    # Convert Decimal values to float for JSON serialization
    services_list = []
    for service in services:
        service_data = dict(service)
        service_data['price_per_session'] = float(service_data['price_per_session'])
        services_list.append(service_data)

    context = {
        'form': form,
        'services_json': json.dumps(services_list),
        'page_title': 'Create Service Transaction'
    }
    return render(request, 'services/transaction_form.html', context)


@login_required
def service_transaction_detail(request, pk):
    """View service transaction details and session logs"""
    transaction = get_object_or_404(ServiceTransaction, pk=pk)
    session_logs = transaction.session_logs.all().order_by('session_number')
    
    context = {
        'transaction': transaction,
        'session_logs': session_logs,
        'page_title': f'Transaction: {transaction.transaction_id}'
    }
    return render(request, 'services/transaction_detail.html', context)


@login_required
def session_log_create(request, transaction_pk):
    """Create a new session log for a transaction"""
    transaction = get_object_or_404(ServiceTransaction, pk=transaction_pk)
    
    # Check if transaction has remaining sessions
    if transaction.sessions_remaining <= 0:
        messages.error(request, 'No remaining sessions available for this transaction.')
        return redirect('service_transaction_detail', pk=transaction.pk)
    
    if request.method == 'POST':
        form = SessionLogForm(request.POST, service_transaction=transaction)
        if form.is_valid():
            session_log = form.save()
            messages.success(
                request, 
                f'Session {session_log.session_number} logged successfully!'
            )
            return redirect('service_transaction_detail', pk=transaction.pk)
    else:
        form = SessionLogForm(service_transaction=transaction)
    
    context = {
        'form': form,
        'transaction': transaction,
        'page_title': f'Log Session - {transaction.transaction_id}'
    }
    return render(request, 'services/session_log_form.html', context)


@login_required
def service_dashboard(request):
    """Dashboard showing service statistics and recent activity"""
    from .models_unified import UnifiedTransaction, UnifiedTransactionLineItem

    # Date ranges
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # Get service line items from unified transactions
    service_line_items = UnifiedTransactionLineItem.objects.filter(
        item_type='service'
    ).select_related('transaction', 'service')

    # Statistics
    stats = {
        'total_services': Service.objects.filter(is_active=True).count(),
        'total_transactions': service_line_items.values('transaction').distinct().count(),
        'active_sessions': service_line_items.filter(
            sessions__gt=models.F('sessions_used')
        ).count(),
        'completed_today': service_line_items.filter(
            transaction__payment_date__date=today
        ).count(),
        'revenue_today': service_line_items.filter(
            transaction__payment_date__date=today
        ).aggregate(
            total=Sum(models.F('unit_price') * models.F('sessions'))
        )['total'] or 0,
        'revenue_week': service_line_items.filter(
            transaction__payment_date__date__gte=week_ago
        ).aggregate(
            total=Sum(models.F('unit_price') * models.F('sessions'))
        )['total'] or 0,
        'revenue_month': service_line_items.filter(
            transaction__payment_date__date__gte=month_ago
        ).aggregate(
            total=Sum(models.F('unit_price') * models.F('sessions'))
        )['total'] or 0,
    }

    # Recent service transactions
    recent_transactions = service_line_items.order_by('-transaction__payment_date')[:10]

    # Popular services
    popular_services = Service.objects.annotate(
        transaction_count=Count('unifiedtransactionlineitem',
                               filter=models.Q(unifiedtransactionlineitem__item_type='service'))
    ).order_by('-transaction_count')[:5]

    context = {
        'stats': stats,
        'recent_transactions': recent_transactions,
        'popular_services': popular_services,
        'page_title': 'Service Dashboard'
    }
    return render(request, 'services/dashboard.html', context)


@login_required
def customer_list(request):
    """List all customers"""
    customers = Customer.objects.all().order_by('name')

    context = {
        'customers': customers,
        'page_title': 'Customer Management'
    }
    return render(request, 'services/customer_list.html', context)


# AJAX Views
@login_required
def generate_gcash_qr(request):
    """AJAX view to generate GCash merchant QR code"""
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)
            amount = float(data.get('amount', 0))

            if amount <= 0:
                return JsonResponse({'error': 'Invalid amount'}, status=400)

            from .utils.payment_utils import PaymentProcessor
            # Generate static merchant QR code (amount is entered by customer in their app)
            qr_result = PaymentProcessor.generate_gcash_merchant_qr()

            return JsonResponse({
                'success': True,
                'qr_code': qr_result['qr_code'],
                'instructions': qr_result['instructions'],
                'merchant_data': qr_result['merchant_data'],
                'amount': amount,  # Pass amount for display purposes
                'qr_type': qr_result['qr_type']
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Invalid request method'}, status=405)

@login_required
def get_service_details(request, service_id):
    """AJAX view to get comprehensive service details"""
    try:
        service = Service.objects.get(id=service_id)

        # Get related statistics
        total_transactions = ServiceTransaction.objects.filter(service=service).count()
        active_transactions = ServiceTransaction.objects.filter(
            service=service,
            status='active'
        ).count()
        total_revenue = ServiceTransaction.objects.filter(
            service=service
        ).aggregate(Sum('amount_paid'))['amount_paid__sum'] or 0

        data = {
            'id': service.id,
            'name': service.name,
            'category': service.get_category_display(),
            'description': service.description,
            'price_per_session': float(service.price_per_session),
            'duration_minutes': service.duration_minutes,
            'is_active': service.is_active,
            'created_at': service.created_at.strftime('%B %d, %Y'),
            'updated_at': service.updated_at.strftime('%B %d, %Y'),
            'statistics': {
                'total_transactions': total_transactions,
                'active_transactions': active_transactions,
                'total_revenue': float(total_revenue)
            }
        }
        return JsonResponse(data)
    except Service.DoesNotExist:
        return JsonResponse({'error': 'Service not found'}, status=404)
