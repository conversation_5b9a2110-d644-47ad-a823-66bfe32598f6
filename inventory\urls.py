from django.urls import path
from . import views, views_service, views_product, views_unified, views_session, views_customer

urlpatterns = [
    path('', views.home, name='home'),

    # Service management URLs
    path('services/', views_service.service_list, name='service_list'),
    path('services/create/', views_service.service_create, name='service_create'),
    path('services/<int:pk>/', views_service.service_detail, name='service_detail'),
    path('services/<int:pk>/edit/', views_service.service_edit, name='service_edit'),
    path('services/dashboard/', views_service.service_dashboard, name='service_dashboard'),

    # Service transaction URLs
    path('transactions/', views_service.service_transaction_list, name='service_transaction_list'),
    path('transactions/create/', views_service.service_transaction_create, name='service_transaction_create'),
    path('transactions/<int:pk>/', views_service.service_transaction_detail, name='service_transaction_detail'),
    path('transactions/<int:transaction_pk>/session/', views_service.session_log_create, name='session_log_create'),

    # Customer management URLs
    path('customers/', views_customer.customer_list, name='customer_list'),
    path('customers/create/', views_customer.customer_create, name='customer_create'),
    path('customers/<int:pk>/', views_customer.customer_detail, name='customer_detail'),
    path('customers/<int:pk>/edit/', views_customer.customer_edit, name='customer_edit'),
    path('customers/<int:pk>/history/', views_customer.customer_history, name='customer_history'),
    path('customers/<int:pk>/delete/', views_customer.customer_delete, name='customer_delete'),

    # Audit trail URLs
    path('audit/', views.audit_trail_list, name='audit_trail_list'),

    # Product management URLs
    path('products/', views_product.product_list, name='product_list'),
    path('products/create/', views_product.product_create, name='product_create'),
    path('products/<int:pk>/', views_product.product_detail, name='product_detail'),
    path('products/<int:pk>/edit/', views_product.product_edit, name='product_edit'),

    # Product transaction URLs
    path('product-transactions/', views_product.product_transaction_list, name='product_transaction_list'),
    path('product-transactions/create/', views_product.product_transaction_create, name='product_transaction_create'),
    path('product-transactions/<int:pk>/', views_product.product_transaction_detail, name='product_transaction_detail'),
    path('stock-adjustment/', views_product.stock_adjustment, name='stock_adjustment'),

    # Unified transaction URLs
    path('unified/', views_unified.unified_transaction_list, name='unified_transaction_list'),
    path('unified/dashboard/', views_unified.unified_dashboard, name='unified_dashboard'),
    path('unified/reports/', views_unified.unified_sales_report, name='unified_sales_report'),
    path('unified/create/', views_unified.unified_transaction_create, name='unified_transaction_create'),
    path('unified/<int:pk>/', views_unified.unified_transaction_detail, name='unified_transaction_detail'),
    path('unified/<int:pk>/edit/', views_unified.unified_transaction_edit, name='unified_transaction_edit'),
    path('unified/<int:pk>/history/', views_unified.unified_transaction_history, name='unified_transaction_history'),

    # Voucher URLs
    path('voucher/validate/', views_unified.validate_voucher, name='validate_voucher'),

    # Cart URLs
    path('cart/data/', views_unified.cart_data, name='cart_data'),
    path('cart/add/', views_unified.cart_add, name='cart_add'),
    path('cart/remove/', views_unified.cart_remove, name='cart_remove'),
    path('cart/clear/', views_unified.cart_clear, name='cart_clear'),

    # Session Management URLs
    path('sessions/', views_session.customer_sessions_dashboard, name='customer_sessions_dashboard'),
    path('sessions/customer/<int:line_item_id>/', views_session.customer_session_detail, name='customer_session_detail'),
    path('sessions/consume/<int:line_item_id>/', views_session.consume_session, name='consume_session'),
    path('sessions/schedule/<int:line_item_id>/', views_session.schedule_appointment, name='schedule_appointment'),
    path('sessions/calendar/', views_session.session_calendar, name='session_calendar'),
    path('sessions/reports/', views_session.session_reports, name='session_reports'),

    # AJAX URLs
    path('api/service/<int:service_id>/', views_service.get_service_details, name='get_service_details'),
    path('api/product/<int:product_id>/', views_product.get_product_details, name='get_product_details'),
    path('api/generate-gcash-qr/', views_service.generate_gcash_qr, name='generate_gcash_qr'),
]
