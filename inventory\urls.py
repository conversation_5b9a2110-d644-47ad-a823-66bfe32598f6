from django.urls import path
from . import views, views_service, views_product, views_unified

urlpatterns = [
    path('', views.home, name='home'),

    # Service management URLs
    path('services/', views_service.service_list, name='service_list'),
    path('services/create/', views_service.service_create, name='service_create'),
    path('services/<int:pk>/', views_service.service_detail, name='service_detail'),
    path('services/<int:pk>/edit/', views_service.service_edit, name='service_edit'),
    path('services/dashboard/', views_service.service_dashboard, name='service_dashboard'),

    # Service transaction URLs
    path('transactions/', views_service.service_transaction_list, name='service_transaction_list'),
    path('transactions/create/', views_service.service_transaction_create, name='service_transaction_create'),
    path('transactions/<int:pk>/', views_service.service_transaction_detail, name='service_transaction_detail'),
    path('transactions/<int:transaction_pk>/session/', views_service.session_log_create, name='session_log_create'),

    # Customer management URLs
    path('customers/', views_service.customer_list, name='customer_list'),

    # Audit trail URLs
    path('audit/', views.audit_trail_list, name='audit_trail_list'),

    # Product management URLs
    path('products/', views_product.product_list, name='product_list'),
    path('products/create/', views_product.product_create, name='product_create'),
    path('products/<int:pk>/', views_product.product_detail, name='product_detail'),
    path('products/<int:pk>/edit/', views_product.product_edit, name='product_edit'),

    # Product transaction URLs
    path('product-transactions/', views_product.product_transaction_list, name='product_transaction_list'),
    path('product-transactions/create/', views_product.product_transaction_create, name='product_transaction_create'),
    path('product-transactions/<int:pk>/', views_product.product_transaction_detail, name='product_transaction_detail'),
    path('stock-adjustment/', views_product.stock_adjustment, name='stock_adjustment'),

    # Unified transaction URLs
    path('unified/', views_unified.unified_transaction_list, name='unified_transaction_list'),
    path('unified/dashboard/', views_unified.unified_dashboard, name='unified_dashboard'),
    path('unified/reports/', views_unified.unified_sales_report, name='unified_sales_report'),
    path('unified/create/', views_unified.unified_transaction_create, name='unified_transaction_create'),
    path('unified/<int:pk>/', views_unified.unified_transaction_detail, name='unified_transaction_detail'),

    # Voucher URLs
    path('voucher/validate/', views_unified.validate_voucher, name='validate_voucher'),

    # Shopping cart AJAX URLs
    path('cart/add/', views_unified.cart_add_item, name='cart_add_item'),
    path('cart/update/', views_unified.cart_update_item, name='cart_update_item'),
    path('cart/remove/', views_unified.cart_remove_item, name='cart_remove_item'),
    path('cart/clear/', views_unified.cart_clear, name='cart_clear'),
    path('cart/data/', views_unified.cart_data, name='cart_data'),

    # AJAX URLs
    path('api/service/<int:service_id>/', views_service.get_service_details, name='get_service_details'),
    path('api/product/<int:product_id>/', views_product.get_product_details, name='get_product_details'),
    path('api/generate-gcash-qr/', views_service.generate_gcash_qr, name='generate_gcash_qr'),
]
