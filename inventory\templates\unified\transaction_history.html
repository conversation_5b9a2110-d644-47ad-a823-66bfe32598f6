{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary mb-0">
                    <i class="fas fa-history me-2"></i>{{ page_title }}
                </h2>
                <div class="d-flex gap-2">
                    <a href="{% url 'unified_transaction_detail' transaction.pk %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Transaction
                    </a>
                </div>
            </div>
            <p class="text-muted mb-0">View all changes and activities for this transaction</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <!-- Transaction Info -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Transaction Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Transaction ID:</strong><br>
                            <span class="text-primary">{{ transaction.transaction_id }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Customer:</strong><br>
                            {{ transaction.customer_name }}
                        </div>
                        <div class="col-md-3">
                            <strong>Total Amount:</strong><br>
                            <span class="text-success">₱{{ transaction.total_amount|floatformat:2 }}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Status:</strong><br>
                            <span class="badge bg-success">{{ transaction.get_status_display }}</span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Created:</strong> {{ transaction.created_at|date:"M d, Y g:i A" }}
                        </div>
                        <div class="col-md-6">
                            <strong>Last Updated:</strong> {{ transaction.updated_at|date:"M d, Y g:i A" }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity History -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>Activity History</h5>
                </div>
                <div class="card-body">
                    {% if activities %}
                        <div class="timeline">
                            {% for activity in activities %}
                            <div class="timeline-item mb-4">
                                <div class="row">
                                    <div class="col-md-2 text-center">
                                        <div class="timeline-marker bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            {% if activity.action == 'LOGIN' %}
                                                <i class="fas fa-sign-in-alt"></i>
                                            {% elif activity.action == 'OTHER' %}
                                                <i class="fas fa-edit"></i>
                                            {% else %}
                                                <i class="fas fa-user"></i>
                                            {% endif %}
                                        </div>
                                        <div class="small text-muted mt-2">
                                            {{ activity.timestamp|date:"M d" }}<br>
                                            {{ activity.timestamp|date:"g:i A" }}
                                        </div>
                                    </div>
                                    <div class="col-md-10">
                                        <div class="card border-left-primary">
                                            <div class="card-body py-3">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1">{{ activity.get_action_display }}</h6>
                                                        <p class="mb-2">{{ activity.description }}</p>
                                                        <div class="small text-muted">
                                                            <i class="fas fa-user me-1"></i>{{ activity.user.get_full_name|default:activity.user.username }}
                                                            {% if activity.ip_address %}
                                                                <span class="ms-3"><i class="fas fa-globe me-1"></i>{{ activity.ip_address }}</span>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                    <span class="badge bg-light text-dark">
                                                        {{ activity.timestamp|date:"M d, Y g:i A" }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Activity History</h5>
                            <p class="text-muted">No recorded activities for this transaction yet.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-database me-2"></i>System Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>Database Record:</strong><br>
                                Created: {{ transaction.created_at|date:"Y-m-d H:i:s" }}<br>
                                Updated: {{ transaction.updated_at|date:"Y-m-d H:i:s" }}
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>Transaction Details:</strong><br>
                                ID: {{ transaction.pk }}<br>
                                Status: {{ transaction.status }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline-item {
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 20px;
    top: 60px;
    width: 2px;
    height: calc(100% - 20px);
    background: #dee2e6;
    z-index: 0;
}

.timeline-marker {
    position: relative;
    z-index: 1;
}

.border-left-primary {
    border-left: 4px solid #007bff !important;
}
</style>
{% endblock %}
