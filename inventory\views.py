from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, JsonResponse
from .models import Service, ServiceTransaction, SessionLog, Customer
from django.db.models import Sum, Count, Q
from django.utils import timezone
import json
from datetime import datetime, timedelta

@login_required
def home(request):
    """Home dashboard - redirect to unified POS dashboard"""
    from django.shortcuts import redirect
    return redirect('unified_dashboard')

@login_required
def reports_dashboard(request):
    """Dashboard for aesthetic services reports"""
    # Get basic statistics
    total_services = Service.objects.filter(is_active=True).count()
    total_transactions = ServiceTransaction.objects.count()
    total_revenue = ServiceTransaction.objects.aggregate(
        total=Sum('amount_paid')
    )['total'] or 0

    # Recent transactions
    recent_transactions = ServiceTransaction.objects.select_related('service').order_by('-payment_date')[:10]

    # Popular services
    popular_services = Service.objects.annotate(
        transaction_count=Count('servicetransaction')
    ).order_by('-transaction_count')[:5]

    context = {
        'total_services': total_services,
        'total_transactions': total_transactions,
        'total_revenue': total_revenue,
        'recent_transactions': recent_transactions,
        'popular_services': popular_services,
    }
    return render(request, 'reports_dashboard.html', context)

@login_required
def direct_forecast_excel(request):
    """Generate Excel report for service forecasting"""
    # For now, return a simple response
    # This can be expanded later to generate actual Excel reports
    return HttpResponse(
        "Excel forecast report generation for aesthetic services - Coming Soon",
        content_type="text/plain"
    )

@login_required
def audit_trail_list(request):
    """List audit trail for aesthetic services"""
    # For now, return a simple page
    # This can be expanded later to show actual audit logs
    context = {
        'page_title': 'Audit Trail',
        'message': 'Audit trail functionality for aesthetic services - Coming Soon'
    }
    return render(request, 'audit_trail_list.html', context)
