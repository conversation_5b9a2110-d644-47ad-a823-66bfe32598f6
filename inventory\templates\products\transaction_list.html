{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>{{ page_title }}
                </h2>
                <a href="{% url 'product_transaction_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>New Transaction
                </a>
            </div>
            <p class="text-muted mb-0">Manage product sales and inventory transactions</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-6 text-primary">{{ stats.total_transactions }}</div>
                    <small class="text-muted">Total Transactions</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-6 text-success">{{ stats.sales_transactions }}</div>
                    <small class="text-muted">Sales</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-6 text-info">{{ stats.total_quantity_sold }}</div>
                    <small class="text-muted">Units Sold</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="display-6 text-warning">₱{{ stats.total_revenue|floatformat:2|intcomma }}</div>
                    <small class="text-muted">Total Revenue</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Form -->
    <div class="card shadow-sm border-0 rounded-3 mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="fas fa-search me-2"></i>Search & Filter
            </h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Search</label>
                    {{ form.search }}
                </div>
                <div class="col-md-2">
                    <label class="form-label">Transaction Type</label>
                    {{ form.transaction_type }}
                </div>
                <div class="col-md-2">
                    <label class="form-label">Product</label>
                    {{ form.product }}
                </div>
                <div class="col-md-2">
                    <label class="form-label">Date From</label>
                    {{ form.date_from }}
                </div>
                <div class="col-md-2">
                    <label class="form-label">Date To</label>
                    {{ form.date_to }}
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>Search
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Transactions Table -->
    <div class="card shadow-sm border-0 rounded-3">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>Transactions
            </h5>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Transaction ID</th>
                            <th>Type</th>
                            <th>Product</th>
                            <th>Quantity</th>
                            <th>Amount</th>
                            <th>Customer</th>
                            <th>Payment</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for line_item in page_obj %}
                        <tr>
                            <td>
                                <a href="{% url 'unified_transaction_detail' line_item.transaction.pk %}" class="text-decoration-none">
                                    {{ line_item.transaction.transaction_id }}
                                </a>
                            </td>
                            <td>
                                <span class="badge bg-success">
                                    Sale
                                </span>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ line_item.product.name }}</strong>
                                    {% if line_item.product.brand %}
                                        <br><small class="text-muted">{{ line_item.product.brand }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>{{ line_item.quantity }} {{ line_item.product.unit }}</td>
                            <td>₱{{ line_item.line_total|floatformat:2|intcomma }}</td>
                            <td>{{ line_item.transaction.customer_name|default:"-" }}</td>
                            <td>
                                {% if line_item.transaction.payment_mode %}
                                    <span class="badge bg-light text-dark">{{ line_item.transaction.get_payment_mode_display }}</span>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>{{ line_item.transaction.payment_date|date:"M d, Y" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'unified_transaction_detail' line_item.transaction.pk %}"
                                       class="btn btn-outline-info" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="card-footer bg-light">
                <nav aria-label="Transactions pagination">
                    <ul class="pagination pagination-sm mb-0 justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                        </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Transactions Found</h5>
                <p class="text-muted">Start by creating your first product transaction.</p>
                <a href="{% url 'product_transaction_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create First Transaction
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
