from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
import random
from datetime import datetime, timedelta

from inventory.models import Product, Service
from inventory.models_unified import UnifiedTransaction, UnifiedTransactionLineItem
from inventory.models_session import SessionConsumption, SessionAppointment


class Command(BaseCommand):
    help = 'Create realistic transaction data covering all test scenarios'

    def handle(self, *args, **options):
        self.stdout.write('Creating comprehensive test scenarios...')
        
        # First, ensure all products have stock
        self.update_product_stock()
        
        # Create realistic transactions
        self.create_comprehensive_scenarios()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created all test scenarios!')
        )

    def update_product_stock(self):
        """Update all products to have realistic stock levels"""
        self.stdout.write('Updating product stock levels...')
        
        products = Product.objects.all()
        for product in products:
            # Set realistic stock levels based on product type
            if 'serum' in product.name.lower() or 'cream' in product.name.lower():
                stock = random.randint(25, 80)  # Skincare products
            elif 'mask' in product.name.lower():
                stock = random.randint(50, 150)  # Face masks
            elif 'cleanser' in product.name.lower() or 'toner' in product.name.lower():
                stock = random.randint(20, 60)  # Cleansers and toners
            else:
                stock = random.randint(15, 45)  # Other products
            
            product.current_stock = stock
            product.save()
            
        self.stdout.write(f'Updated stock for {products.count()} products')

    def create_comprehensive_scenarios(self):
        """Create all requested test scenarios"""
        
        # Realistic Filipino customer data
        customers = [
            {'name': 'Maria Clara Santos', 'phone': '+639171234567', 'email': '<EMAIL>'},
            {'name': 'Ana Beatriz Reyes', 'phone': '+639282345678', 'email': '<EMAIL>'},
            {'name': 'Carmen Isabella Cruz', 'phone': '+639393456789', 'email': '<EMAIL>'},
            {'name': 'Rosa Gabriela Gonzales', 'phone': '+639174567890', 'email': '<EMAIL>'},
            {'name': 'Elena Sofia Mendoza', 'phone': '+639285678901', 'email': '<EMAIL>'},
            {'name': 'Sofia Camila Dela Cruz', 'phone': '+639396789012', 'email': '<EMAIL>'},
            {'name': 'Isabella Valentina Garcia', 'phone': '+639177890123', 'email': '<EMAIL>'},
            {'name': 'Camila Andrea Rodriguez', 'phone': '+639288901234', 'email': '<EMAIL>'},
            {'name': 'Valentina Nicole Lopez', 'phone': '+639399012345', 'email': '<EMAIL>'},
            {'name': 'Gabriela Michelle Morales', 'phone': '+639170123456', 'email': '<EMAIL>'},
            {'name': 'Stephanie Mae Villanueva', 'phone': '+639281234567', 'email': '<EMAIL>'},
            {'name': 'Andrea Joy Fernandez', 'phone': '+639392345678', 'email': '<EMAIL>'},
        ]
        
        staff_members = [
            'Dr. Sarah Martinez', 'Nurse Joy Aquino', 'Therapist Anna Lim', 
            'Dr. Michael Santos', 'Aesthetician Lisa Chen', 'Dermatologist Mark Tan'
        ]
        
        # Get available products and services
        products = list(Product.objects.all())
        services = list(Service.objects.filter(is_active=True))
        
        scenarios = [
            # Product transactions
            ('product_no_discount', self.create_product_no_discount),
            ('product_with_discount', self.create_product_with_discount),
            
            # Service transactions
            ('service_no_discount', self.create_service_no_discount),
            ('service_with_discount', self.create_service_with_discount),
            
            # Mixed transactions
            ('mixed_no_discount', self.create_mixed_no_discount),
            ('mixed_with_discount', self.create_mixed_with_discount),
            
            # Session scenarios
            ('sessions_completed', self.create_sessions_completed),
            ('sessions_partial', self.create_sessions_partial),
            ('sessions_with_appointments', self.create_sessions_with_appointments),
            ('sessions_not_started', self.create_sessions_not_started),
        ]
        
        # Create scenarios
        for i, (scenario_name, scenario_func) in enumerate(scenarios):
            customer = customers[i % len(customers)]
            
            try:
                scenario_func(customer, products, services, staff_members, i + 1)
                self.stdout.write(f'✓ Created {scenario_name} scenario')
            except Exception as e:
                self.stdout.write(f'✗ Error creating {scenario_name}: {e}')

    def create_product_no_discount(self, customer, products, services, staff_members, num):
        """Product transaction without discount"""
        payment_mode = 'cash'
        payment_date = timezone.now() - timedelta(days=random.randint(1, 15))

        # Calculate subtotal first
        selected_products = random.sample(products, min(3, len(products)))
        subtotal = Decimal('0.00')
        line_items_data = []

        for product in selected_products:
            quantity = random.randint(1, 2)
            line_total = product.selling_price * quantity
            subtotal += line_total
            line_items_data.append({
                'product': product,
                'quantity': quantity,
                'unit_price': product.selling_price
            })

        transaction = UnifiedTransaction.objects.create(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=0,
            staff_received_by=random.choice(staff_members),
            remarks='Product purchase - No discount applied',
            cash_amount_received=subtotal + Decimal('100.00'),  # Add some change
            subtotal=subtotal,
        )

        # Create line items
        for item_data in line_items_data:
            UnifiedTransactionLineItem.objects.create(
                transaction=transaction,
                item_type='product',
                product=item_data['product'],
                quantity=item_data['quantity'],
                unit_price=item_data['unit_price'],
            )

    def create_product_with_discount(self, customer, products, services, staff_members, num):
        """Product transaction with discount"""
        payment_mode = 'gcash'
        payment_date = timezone.now() - timedelta(days=random.randint(5, 20))
        
        transaction = UnifiedTransaction.objects.create(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=10,
            staff_received_by=random.choice(staff_members),
            remarks='Product purchase - 10% loyalty discount applied',
            gcash_reference_number=f'GC{random.randint(100000000000, 999999999999)}',
        )
        
        # Add premium products
        selected_products = random.sample(products, 2)
        for product in selected_products:
            quantity = random.randint(1, 3)
            UnifiedTransactionLineItem.objects.create(
                transaction=transaction,
                item_type='product',
                product=product,
                quantity=quantity,
                unit_price=product.selling_price,
            )

    def create_service_no_discount(self, customer, products, services, staff_members, num):
        """Service transaction without discount"""
        payment_mode = 'cash'
        payment_date = timezone.now() - timedelta(days=random.randint(10, 40))
        
        transaction = UnifiedTransaction.objects.create(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=0,
            staff_received_by=random.choice(staff_members),
            remarks='Facial treatment package - Regular pricing',
            cash_amount_received=Decimal('5000.00'),
        )
        
        # Add facial service
        service = random.choice([s for s in services if 'facial' in s.name.lower()])
        sessions = 6
        
        line_item = UnifiedTransactionLineItem.objects.create(
            transaction=transaction,
            item_type='service',
            service=service,
            sessions=sessions,
            unit_price=service.price_per_session,
        )
        
        # Create partial session consumption
        self.create_partial_sessions(line_item, staff_members, payment_date, 3)

    def create_service_with_discount(self, customer, products, services, staff_members, num):
        """Service transaction with discount"""
        payment_mode = 'gcash'
        payment_date = timezone.now() - timedelta(days=random.randint(15, 50))
        
        transaction = UnifiedTransaction.objects.create(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=15,
            staff_received_by=random.choice(staff_members),
            remarks='Premium service package - 15% new customer discount',
            gcash_reference_number=f'GC{random.randint(100000000000, 999999999999)}',
        )
        
        # Add premium service
        service = random.choice(services)
        sessions = 8
        
        line_item = UnifiedTransactionLineItem.objects.create(
            transaction=transaction,
            item_type='service',
            service=service,
            sessions=sessions,
            unit_price=service.price_per_session,
        )
        
        # Create session consumption with appointments
        self.create_sessions_with_future_appointments(line_item, staff_members, payment_date)

    def create_mixed_no_discount(self, customer, products, services, staff_members, num):
        """Mixed transaction without discount"""
        payment_mode = 'cash'
        payment_date = timezone.now() - timedelta(days=random.randint(5, 25))
        
        transaction = UnifiedTransaction.objects.create(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=0,
            staff_received_by=random.choice(staff_members),
            remarks='Complete skincare package - Products + Services',
            cash_amount_received=Decimal('8000.00'),
        )
        
        # Add products
        selected_products = random.sample(products, 2)
        for product in selected_products:
            UnifiedTransactionLineItem.objects.create(
                transaction=transaction,
                item_type='product',
                product=product,
                quantity=1,
                unit_price=product.selling_price,
            )
        
        # Add service
        service = random.choice(services)
        line_item = UnifiedTransactionLineItem.objects.create(
            transaction=transaction,
            item_type='service',
            service=service,
            sessions=5,
            unit_price=service.price_per_session,
        )
        
        self.create_partial_sessions(line_item, staff_members, payment_date, 2)

    def create_mixed_with_discount(self, customer, products, services, staff_members, num):
        """Mixed transaction with discount"""
        payment_mode = 'gcash'
        payment_date = timezone.now() - timedelta(days=random.randint(8, 35))
        
        transaction = UnifiedTransaction.objects.create(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=12,
            staff_received_by=random.choice(staff_members),
            remarks='VIP package - Products + Services with 12% discount',
            gcash_reference_number=f'GC{random.randint(100000000000, 999999999999)}',
        )
        
        # Add multiple products
        selected_products = random.sample(products, 3)
        for product in selected_products:
            UnifiedTransactionLineItem.objects.create(
                transaction=transaction,
                item_type='product',
                product=product,
                quantity=random.randint(1, 2),
                unit_price=product.selling_price,
            )
        
        # Add service
        service = random.choice(services)
        line_item = UnifiedTransactionLineItem.objects.create(
            transaction=transaction,
            item_type='service',
            service=service,
            sessions=10,
            unit_price=service.price_per_session,
        )
        
        self.create_completed_sessions(line_item, staff_members, payment_date)

    def create_sessions_completed(self, customer, products, services, staff_members, num):
        """Service with all sessions completed"""
        payment_mode = 'cash'
        payment_date = timezone.now() - timedelta(days=60)
        
        transaction = UnifiedTransaction.objects.create(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=0,
            staff_received_by=random.choice(staff_members),
            remarks='Treatment package - All sessions completed',
            cash_amount_received=Decimal('4500.00'),
        )
        
        service = random.choice(services)
        line_item = UnifiedTransactionLineItem.objects.create(
            transaction=transaction,
            item_type='service',
            service=service,
            sessions=5,
            unit_price=service.price_per_session,
        )
        
        self.create_completed_sessions(line_item, staff_members, payment_date)

    def create_sessions_partial(self, customer, products, services, staff_members, num):
        """Service with partial sessions completed"""
        payment_mode = 'gcash'
        payment_date = timezone.now() - timedelta(days=30)
        
        transaction = UnifiedTransaction.objects.create(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=5,
            staff_received_by=random.choice(staff_members),
            remarks='Treatment package - In progress',
            gcash_reference_number=f'GC{random.randint(100000000000, 999999999999)}',
        )
        
        service = random.choice(services)
        line_item = UnifiedTransactionLineItem.objects.create(
            transaction=transaction,
            item_type='service',
            service=service,
            sessions=8,
            unit_price=service.price_per_session,
        )
        
        self.create_partial_sessions(line_item, staff_members, payment_date, 4)

    def create_sessions_with_appointments(self, customer, products, services, staff_members, num):
        """Service with some sessions done and future appointments"""
        payment_mode = 'cash'
        payment_date = timezone.now() - timedelta(days=20)
        
        transaction = UnifiedTransaction.objects.create(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=0,
            staff_received_by=random.choice(staff_members),
            remarks='Treatment package - With scheduled appointments',
            cash_amount_received=Decimal('6000.00'),
        )
        
        service = random.choice(services)
        line_item = UnifiedTransactionLineItem.objects.create(
            transaction=transaction,
            item_type='service',
            service=service,
            sessions=7,
            unit_price=service.price_per_session,
        )
        
        self.create_sessions_with_future_appointments(line_item, staff_members, payment_date)

    def create_sessions_not_started(self, customer, products, services, staff_members, num):
        """Service paid but no sessions consumed yet"""
        payment_mode = 'gcash'
        payment_date = timezone.now() - timedelta(days=5)
        
        transaction = UnifiedTransaction.objects.create(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=8,
            staff_received_by=random.choice(staff_members),
            remarks='New customer package - Sessions not started yet',
            gcash_reference_number=f'GC{random.randint(100000000000, 999999999999)}',
        )
        
        service = random.choice(services)
        UnifiedTransactionLineItem.objects.create(
            transaction=transaction,
            item_type='service',
            service=service,
            sessions=6,
            unit_price=service.price_per_session,
        )
        # No sessions created - customer paid but hasn't started

    def create_completed_sessions(self, line_item, staff_members, payment_date):
        """Create all sessions as completed"""
        for session_num in range(1, line_item.sessions + 1):
            session_date = payment_date + timedelta(days=7 * session_num)
            SessionConsumption.objects.create(
                line_item=line_item,
                session_number=session_num,
                session_date=session_date,
                staff_performed_by=random.choice(staff_members),
                duration_minutes=random.randint(60, 90),
                notes=f'Session {session_num} completed successfully. Excellent progress.',
                customer_feedback=random.choice([
                    'Very satisfied with results',
                    'Excellent service quality',
                    'Noticeable improvement',
                    'Professional and caring staff'
                ]),
                status='completed'
            )

    def create_partial_sessions(self, line_item, staff_members, payment_date, completed_count):
        """Create partial sessions completed"""
        for session_num in range(1, completed_count + 1):
            session_date = payment_date + timedelta(days=7 * session_num)
            SessionConsumption.objects.create(
                line_item=line_item,
                session_number=session_num,
                session_date=session_date,
                staff_performed_by=random.choice(staff_members),
                duration_minutes=random.randint(60, 90),
                notes=f'Session {session_num} - Treatment progressing well.',
                customer_feedback='Satisfied with progress',
                status='completed'
            )

    def create_sessions_with_future_appointments(self, line_item, staff_members, payment_date):
        """Create some completed sessions and future appointments"""
        completed = 3
        
        # Complete some sessions
        for session_num in range(1, completed + 1):
            session_date = payment_date + timedelta(days=7 * session_num)
            SessionConsumption.objects.create(
                line_item=line_item,
                session_number=session_num,
                session_date=session_date,
                staff_performed_by=random.choice(staff_members),
                duration_minutes=random.randint(60, 90),
                notes=f'Session {session_num} completed as scheduled.',
                customer_feedback='Good experience',
                status='completed'
            )
        
        # Schedule future appointments
        for session_num in range(completed + 1, min(completed + 3, line_item.sessions + 1)):
            appointment_date = timezone.now() + timedelta(days=7 * (session_num - completed))
            SessionAppointment.objects.create(
                line_item=line_item,
                appointment_date=appointment_date,
                session_number=session_num,
                assigned_staff=random.choice(staff_members),
                status=random.choice(['scheduled', 'confirmed']),
                appointment_notes=f'Session {session_num} scheduled appointment'
            )
