{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary mb-0">
                    <i class="fas fa-user me-2"></i>{{ customer.name }}
                </h2>
                <div class="d-flex gap-2">
                    <a href="{% url 'customer_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Customers
                    </a>
                    <a href="{% url 'customer_edit' customer.pk %}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>Edit Customer
                    </a>
                    <a href="{% url 'customer_history' customer.pk %}" class="btn btn-info">
                        <i class="fas fa-history me-1"></i>View History
                    </a>
                </div>
            </div>
            <p class="text-muted mb-0">Customer details and transaction history</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <!-- Customer Information -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Customer Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Basic Information</h6>
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ customer.name }}</td>
                                </tr>
                                {% if customer.date_of_birth %}
                                <tr>
                                    <td><strong>Date of Birth:</strong></td>
                                    <td>{{ customer.date_of_birth|date:"M d, Y" }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>Customer Since:</strong></td>
                                    <td>{{ customer.created_at|date:"M d, Y" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Last Updated:</strong></td>
                                    <td>{{ customer.updated_at|date:"M d, Y g:i A" }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Contact Information</h6>
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>{{ customer.phone|default:"Not provided" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ customer.email|default:"Not provided" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Address:</strong></td>
                                    <td>{{ customer.address|default:"Not provided" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Emergency Contact:</strong></td>
                                    <td>{{ customer.emergency_contact|default:"Not provided" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if customer.medical_notes %}
                    <hr>
                    <h6 class="text-primary">Medical Notes</h6>
                    <div class="alert alert-info">
                        <i class="fas fa-notes-medical me-2"></i>
                        {{ customer.medical_notes|linebreaks }}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Transaction Statistics -->
        <div class="col-md-4">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Transaction Summary</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <h3 class="text-success">{{ transaction_stats.total_transactions }}</h3>
                        <p class="text-muted mb-0">Total Transactions</p>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Total Spent:</span>
                        <span class="fw-bold text-success">₱{{ transaction_stats.total_spent|floatformat:2 }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Average Transaction:</span>
                        <span class="fw-bold">₱{{ transaction_stats.avg_transaction|floatformat:2 }}</span>
                    </div>
                    {% if transaction_stats.last_transaction %}
                    <div class="d-flex justify-content-between">
                        <span>Last Visit:</span>
                        <span>{{ transaction_stats.last_transaction.payment_date|date:"M d, Y" }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card border-primary mt-3">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'unified_transaction_create' %}?customer={{ customer.name|urlencode }}" class="btn btn-success btn-sm">
                            <i class="fas fa-plus me-2"></i>New Transaction
                        </a>
                        <a href="{% url 'customer_edit' customer.pk %}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit me-2"></i>Edit Customer
                        </a>
                        <a href="{% url 'customer_history' customer.pk %}" class="btn btn-info btn-sm">
                            <i class="fas fa-history me-2"></i>View History
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction History -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Transaction History</h5>
                </div>
                <div class="card-body">
                    {% if transactions_page %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Transaction ID</th>
                                        <th>Date</th>
                                        <th>Items</th>
                                        <th>Payment Method</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for transaction in transactions_page %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'unified_transaction_detail' transaction.pk %}" class="text-decoration-none">
                                                {{ transaction.transaction_id }}
                                            </a>
                                        </td>
                                        <td>{{ transaction.payment_date|date:"M d, Y g:i A" }}</td>
                                        <td>
                                            <span class="badge bg-light text-dark">{{ transaction.line_items_count }} items</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ transaction.get_payment_mode_display }}</span>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">₱{{ transaction.total_amount|floatformat:2 }}</span>
                                        </td>
                                        <td>
                                            {% if transaction.status == 'active' %}
                                                <span class="badge bg-success">{{ transaction.get_status_display }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ transaction.get_status_display }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'unified_transaction_detail' transaction.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if transactions_page.has_other_pages %}
                        <nav aria-label="Transaction pagination">
                            <ul class="pagination justify-content-center">
                                {% if transactions_page.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1">First</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ transactions_page.previous_page_number }}">Previous</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">{{ transactions_page.number }} of {{ transactions_page.paginator.num_pages }}</span>
                                </li>

                                {% if transactions_page.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ transactions_page.next_page_number }}">Next</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ transactions_page.paginator.num_pages }}">Last</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Transactions Yet</h5>
                            <p class="text-muted">This customer hasn't made any transactions yet.</p>
                            <a href="{% url 'unified_transaction_create' %}?customer={{ customer.name|urlencode }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create First Transaction
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
