from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
from django.core.mail import send_mail
from django.conf import settings
from django.db.models import Sum, F, Q, Count
import logging
import random

logger = logging.getLogger(__name__)
User = get_user_model()


class Service(models.Model):
    """Model for aesthetic services offered at REMR Aesthetic Centre"""
    SERVICE_CATEGORIES = [
        ('facial_treatment', 'Facial Treatment'),
        ('gluta_treatment', 'Gluta Treatment'),
        ('ipl_treatment', 'IPL Treatment'),
        ('aesthetic_procedure', 'Aesthetic Procedure'),
        ('skincare', 'Skincare Treatment'),
        ('massage_therapy', 'Massage Therapy'),
        ('training_course', 'Training Course'),
        ('certification', 'Certification'),
        ('injectable_training', 'Injectable Training'),
        ('other', 'Other')
    ]
    
    name = models.CharField(max_length=255, help_text="Service name (e.g., Gluta Drip, IPL Hair Removal)")
    category = models.CharField(max_length=20, choices=SERVICE_CATEGORIES, default='gluta_treatment')
    description = models.TextField(blank=True, help_text="Detailed description of the service")
    price_per_session = models.DecimalField(max_digits=10, decimal_places=2, help_text="Regular price per individual session")
    promotional_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Special promotional price")
    cash_discount_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Special cash payment price")
    duration_minutes = models.IntegerField(default=60, help_text="Duration of each session in minutes")

    # Promotional settings
    is_on_promotion = models.BooleanField(default=False, help_text="Is this service currently on promotion?")
    promotion_start_date = models.DateTimeField(null=True, blank=True, help_text="Promotion start date")
    promotion_end_date = models.DateTimeField(null=True, blank=True, help_text="Promotion end date")

    is_active = models.BooleanField(default=True, help_text="Whether this service is currently offered")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['category', 'name']
        verbose_name = "Service"
        verbose_name_plural = "Services"
    
    def __str__(self):
        return f"{self.name} - ₱{self.price_per_session}/session"

    @property
    def effective_price(self):
        """Get the current effective price based on promotional settings"""
        if self.is_on_promotion and self.promotional_price:
            # Check if promotion is currently active
            now = timezone.now()
            if (not self.promotion_start_date or self.promotion_start_date <= now) and \
               (not self.promotion_end_date or self.promotion_end_date >= now):
                return self.promotional_price
        return self.price_per_session

    @property
    def cash_price(self):
        """Get the cash discount price if available, otherwise regular price"""
        return self.cash_discount_price or self.effective_price

    @property
    def is_promotion_active(self):
        """Check if promotion is currently active"""
        if not self.is_on_promotion or not self.promotional_price:
            return False

        now = timezone.now()
        start_ok = not self.promotion_start_date or self.promotion_start_date <= now
        end_ok = not self.promotion_end_date or self.promotion_end_date >= now
        return start_ok and end_ok


class ServiceTransaction(models.Model):
    """Model for service-based transactions (customer purchases service packages)"""
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded')
    ]
    
    PAYMENT_STATUS_CHOICES = [
        ('paid', 'Fully Paid'),
        ('partial', 'Partially Paid'),
        ('pending', 'Pending Payment')
    ]

    PAYMENT_MODE_CHOICES = [
        ('cash', 'Cash'),
        ('gcash', 'GCash'),
        ('bank_transfer', 'Bank Transfer'),
        ('card', 'Credit/Debit Card')
    ]
    
    # Transaction identification
    transaction_id = models.CharField(max_length=20, unique=True, help_text="Unique transaction ID")
    
    # Customer information
    customer_name = models.CharField(max_length=255, help_text="Customer's full name")
    customer_phone = models.CharField(max_length=20, blank=True, help_text="Customer's phone number")
    customer_email = models.EmailField(blank=True, help_text="Customer's email address")
    
    # Service details
    service = models.ForeignKey(Service, on_delete=models.CASCADE, help_text="Service purchased")
    total_sessions = models.IntegerField(help_text="Total number of sessions paid for")
    sessions_used = models.IntegerField(default=0, help_text="Number of sessions already consumed")
    
    # Payment information
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2, help_text="Total amount paid")
    payment_date = models.DateTimeField(help_text="When the payment was made")
    payment_status = models.CharField(max_length=10, choices=PAYMENT_STATUS_CHOICES, default='paid')
    payment_mode = models.CharField(max_length=15, choices=PAYMENT_MODE_CHOICES, default='cash', help_text="Mode of payment")

    # Payment verification fields
    cash_amount_received = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Cash amount received (for cash payments)")
    cash_change_given = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Change given to customer")
    gcash_reference_number = models.CharField(max_length=50, blank=True, help_text="GCash transaction reference number")
    bank_reference_number = models.CharField(max_length=50, blank=True, help_text="Bank transfer reference number")
    bank_name = models.CharField(max_length=100, blank=True, help_text="Bank name for transfer")
    card_authorization_code = models.CharField(max_length=20, blank=True, help_text="Credit/Debit card authorization code")
    card_last_four_digits = models.CharField(max_length=4, blank=True, help_text="Last 4 digits of card")
    card_type = models.CharField(max_length=20, blank=True, help_text="Card type (Visa, Mastercard, etc.)")
    payment_verified = models.BooleanField(default=False, help_text="Whether payment has been verified")
    payment_verification_notes = models.TextField(blank=True, help_text="Notes about payment verification")
    
    # Status and dates
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='active')
    expiry_date = models.DateField(null=True, blank=True, help_text="Expiry date for unused sessions")
    
    # Additional information
    remarks = models.TextField(blank=True, help_text="Additional notes or comments")
    staff_received_by = models.CharField(max_length=255, blank=True, help_text="Staff member who received payment")
    
    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-payment_date']
        verbose_name = "Service Transaction"
        verbose_name_plural = "Service Transactions"
    
    def __str__(self):
        return f"{self.transaction_id} - {self.customer_name} - {self.service.name}"
    
    @property
    def sessions_remaining(self):
        """Calculate remaining sessions"""
        return max(0, self.total_sessions - self.sessions_used)
    
    @property
    def is_expired(self):
        """Check if the transaction has expired"""
        if self.expiry_date:
            return timezone.now().date() > self.expiry_date
        return False
    
    @property
    def completion_percentage(self):
        """Calculate completion percentage"""
        if self.total_sessions == 0:
            return 0
        return (self.sessions_used / self.total_sessions) * 100
    
    def save(self, *args, **kwargs):
        # Generate transaction ID if not provided
        if not self.transaction_id:
            date_str = timezone.now().strftime('%Y%m%d')
            random_num = random.randint(1000, 9999)
            self.transaction_id = f"ST{date_str}{random_num}"
        
        # Auto-update status based on sessions
        if self.sessions_used >= self.total_sessions:
            self.status = 'completed'
        elif self.is_expired and self.status == 'active':
            self.status = 'expired'
        
        super().save(*args, **kwargs)


class SessionLog(models.Model):
    """Model to track individual service sessions"""
    service_transaction = models.ForeignKey(ServiceTransaction, on_delete=models.CASCADE, related_name='session_logs')
    session_date = models.DateTimeField(help_text="When the session was performed")
    session_number = models.IntegerField(help_text="Session number (1, 2, 3, etc.)")
    staff_performed_by = models.CharField(max_length=255, blank=True, help_text="Staff member who performed the service")
    notes = models.TextField(blank=True, help_text="Session notes or observations")

    # Session attachments (before/after photos, progress notes, etc.)
    attachment_1 = models.FileField(upload_to='session_files/', blank=True, null=True, help_text="Session document (before/after photo, progress note, etc.)")
    attachment_1_description = models.CharField(max_length=255, blank=True, help_text="Description of attachment 1")
    attachment_2 = models.FileField(upload_to='session_files/', blank=True, null=True, help_text="Additional session document")
    attachment_2_description = models.CharField(max_length=255, blank=True, help_text="Description of attachment 2")
    attachment_3 = models.FileField(upload_to='session_files/', blank=True, null=True, help_text="Additional session document")
    attachment_3_description = models.CharField(max_length=255, blank=True, help_text="Description of attachment 3")

    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['session_number']
        unique_together = ['service_transaction', 'session_number']
        verbose_name = "Session Log"
        verbose_name_plural = "Session Logs"
    
    def __str__(self):
        return f"{self.service_transaction.transaction_id} - Session {self.session_number}"
    
    def save(self, *args, **kwargs):
        # Auto-increment sessions_used in the parent transaction
        if not self.pk:  # Only for new sessions
            self.service_transaction.sessions_used += 1
            self.service_transaction.save()
        super().save(*args, **kwargs)


class Customer(models.Model):
    """Model to store customer information"""
    name = models.CharField(max_length=255, help_text="Customer's full name")
    phone = models.CharField(max_length=20, blank=True, help_text="Customer's phone number")
    email = models.EmailField(blank=True, help_text="Customer's email address")
    address = models.TextField(blank=True, help_text="Customer's address")
    date_of_birth = models.DateField(null=True, blank=True, help_text="Customer's date of birth")
    emergency_contact = models.CharField(max_length=255, blank=True, help_text="Emergency contact information")
    medical_notes = models.TextField(blank=True, help_text="Any medical notes or allergies")

    # Customer attachments (ID, medical records, etc.)
    attachment_1 = models.FileField(upload_to='customer_files/', blank=True, null=True, help_text="Customer document (ID, medical record, etc.)")
    attachment_1_description = models.CharField(max_length=255, blank=True, help_text="Description of attachment 1")
    attachment_2 = models.FileField(upload_to='customer_files/', blank=True, null=True, help_text="Additional customer document")
    attachment_2_description = models.CharField(max_length=255, blank=True, help_text="Description of attachment 2")
    attachment_3 = models.FileField(upload_to='customer_files/', blank=True, null=True, help_text="Additional customer document")
    attachment_3_description = models.CharField(max_length=255, blank=True, help_text="Description of attachment 3")

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = "Customer"
        verbose_name_plural = "Customers"
    
    def __str__(self):
        return self.name
    
    @property
    def total_transactions(self):
        """Get total number of transactions for this customer"""
        from .models_unified import UnifiedTransaction
        return UnifiedTransaction.objects.filter(customer_name=self.name).count()

    @property
    def total_spent(self):
        """Get total amount spent by this customer"""
        from .models_unified import UnifiedTransaction
        return UnifiedTransaction.objects.filter(
            customer_name=self.name
        ).aggregate(total=Sum('total_amount'))['total'] or 0

    def get_all_attachments(self):
        """Get all attachments from customer profile and transactions"""
        from .models_unified import UnifiedTransaction
        import os

        attachments = []

        # First, add customer profile attachments
        for i in range(1, 4):
            attachment_field = getattr(self, f'attachment_{i}', None)
            description_field = getattr(self, f'attachment_{i}_description', '')

            if attachment_field and attachment_field.name:
                file_extension = os.path.splitext(attachment_field.name)[1].lower()
                is_image = file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']

                attachments.append({
                    'file_url': attachment_field.url,
                    'description': description_field or f'Customer Document {i}',
                    'source': 'Customer Profile',
                    'source_id': f'Customer-{self.pk}',
                    'source_date': self.updated_at,
                    'is_image': is_image,
                    'file_name': os.path.basename(attachment_field.name),
                    'type': 'customer'
                })

        # Then, add transaction attachments
        transactions = UnifiedTransaction.objects.filter(customer_name=self.name)

        for transaction in transactions:
            # Check each attachment field
            for i in range(1, 4):
                attachment_field = getattr(transaction, f'attachment_{i}', None)
                description_field = getattr(transaction, f'attachment_{i}_description', '')

                if attachment_field and attachment_field.name:
                    file_extension = os.path.splitext(attachment_field.name)[1].lower()
                    is_image = file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']

                    attachments.append({
                        'file_url': attachment_field.url,
                        'description': description_field or f'Transaction Document {i}',
                        'source': f'Transaction {transaction.transaction_id}',
                        'source_id': transaction.transaction_id,
                        'source_date': transaction.payment_date,
                        'is_image': is_image,
                        'file_name': os.path.basename(attachment_field.name),
                        'type': 'transaction'
                    })

        # Sort by date (newest first)
        attachments.sort(key=lambda x: x['source_date'], reverse=True)
        return attachments


class Product(models.Model):
    """Model for aesthetic products sold at the center"""
    PRODUCT_CATEGORIES = [
        ('skincare', 'Skincare Products'),
        ('supplements', 'Supplements & Vitamins'),
        ('cosmetics', 'Cosmetics & Beauty'),
        ('equipment', 'Equipment & Tools'),
        ('accessories', 'Accessories'),
        ('treatment_kits', 'Treatment Kits'),
        ('serums', 'Serums & Treatments'),
        ('masks', 'Masks & Peels'),
        ('other', 'Other')
    ]

    UNIT_CHOICES = [
        ('piece', 'Piece'),
        ('bottle', 'Bottle'),
        ('tube', 'Tube'),
        ('box', 'Box'),
        ('set', 'Set'),
        ('ml', 'Milliliter'),
        ('gram', 'Gram'),
        ('kg', 'Kilogram')
    ]

    # Basic product information
    name = models.CharField(max_length=255, help_text="Product name")
    brand = models.CharField(max_length=100, blank=True, help_text="Product brand")
    category = models.CharField(max_length=20, choices=PRODUCT_CATEGORIES, default='skincare')
    description = models.TextField(blank=True, help_text="Product description")

    # Inventory information
    sku = models.CharField(max_length=50, unique=True, help_text="Stock Keeping Unit (SKU)")
    unit = models.CharField(max_length=10, choices=UNIT_CHOICES, default='piece', help_text="Unit of measurement")
    current_stock = models.IntegerField(default=0, help_text="Current stock quantity")
    minimum_stock = models.IntegerField(default=5, help_text="Minimum stock level for alerts")
    maximum_stock = models.IntegerField(default=100, help_text="Maximum stock capacity")

    # Pricing information
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Cost price per unit")
    selling_price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Regular selling price per unit")
    promotional_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Special promotional price")
    cash_discount_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Special cash payment price")

    # Promotional settings
    is_on_promotion = models.BooleanField(default=False, help_text="Is this product currently on promotion?")
    promotion_start_date = models.DateTimeField(null=True, blank=True, help_text="Promotion start date")
    promotion_end_date = models.DateTimeField(null=True, blank=True, help_text="Promotion end date")

    # Product status
    is_active = models.BooleanField(default=True, help_text="Whether this product is available for sale")

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['category', 'name']
        verbose_name = "Product"
        verbose_name_plural = "Products"

    def __str__(self):
        return f"{self.name} - ₱{self.selling_price}/{self.unit}"

    @property
    def effective_price(self):
        """Get the current effective price based on promotional settings"""
        if self.is_on_promotion and self.promotional_price:
            # Check if promotion is currently active
            now = timezone.now()
            if (not self.promotion_start_date or self.promotion_start_date <= now) and \
               (not self.promotion_end_date or self.promotion_end_date >= now):
                return self.promotional_price
        return self.selling_price

    @property
    def cash_price(self):
        """Get the cash discount price if available, otherwise regular price"""
        return self.cash_discount_price or self.effective_price

    @property
    def is_promotion_active(self):
        """Check if promotion is currently active"""
        if not self.is_on_promotion or not self.promotional_price:
            return False

        now = timezone.now()
        start_ok = not self.promotion_start_date or self.promotion_start_date <= now
        end_ok = not self.promotion_end_date or self.promotion_end_date >= now
        return start_ok and end_ok

    @property
    def profit_margin(self):
        """Calculate profit margin percentage"""
        if self.cost_price > 0:
            return ((self.selling_price - self.cost_price) / self.cost_price) * 100
        return 0

    @property
    def is_low_stock(self):
        """Check if product is below minimum stock level"""
        return self.current_stock <= self.minimum_stock

    @property
    def stock_status(self):
        """Get stock status description"""
        if self.current_stock == 0:
            return "Out of Stock"
        elif self.is_low_stock:
            return "Low Stock"
        elif self.current_stock >= self.maximum_stock:
            return "Overstocked"
        else:
            return "In Stock"


class ProductTransaction(models.Model):
    """Model for product sales and inventory movements"""
    TRANSACTION_TYPES = [
        ('sale', 'Sale'),
        ('purchase', 'Purchase/Restock'),
        ('adjustment', 'Stock Adjustment'),
        ('return', 'Return'),
        ('damage', 'Damage/Loss')
    ]

    PAYMENT_MODE_CHOICES = [
        ('cash', 'Cash'),
        ('gcash', 'GCash')
    ]

    # Transaction identification
    transaction_id = models.CharField(max_length=20, unique=True, help_text="Unique transaction ID")
    transaction_type = models.CharField(max_length=15, choices=TRANSACTION_TYPES, default='sale')

    # Product and quantity
    product = models.ForeignKey(Product, on_delete=models.CASCADE, help_text="Product involved in transaction")
    quantity = models.IntegerField(help_text="Quantity of products")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Price per unit at time of transaction")
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, help_text="Total transaction amount")

    # Customer information (for sales)
    customer_name = models.CharField(max_length=255, blank=True, help_text="Customer name (for sales)")
    customer_phone = models.CharField(max_length=20, blank=True, help_text="Customer phone")
    customer_email = models.EmailField(blank=True, help_text="Customer email")

    # Payment information (for sales)
    payment_mode = models.CharField(max_length=15, choices=PAYMENT_MODE_CHOICES, blank=True, help_text="Payment method")
    payment_date = models.DateTimeField(null=True, blank=True, help_text="When payment was received")

    # Payment verification fields
    cash_amount_received = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Cash amount received (for cash payments)")
    cash_change_given = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Change given to customer")
    gcash_reference_number = models.CharField(max_length=50, blank=True, help_text="GCash transaction reference number")
    bank_reference_number = models.CharField(max_length=50, blank=True, help_text="Bank transfer reference number")
    bank_name = models.CharField(max_length=100, blank=True, help_text="Bank name for transfer")
    card_authorization_code = models.CharField(max_length=20, blank=True, help_text="Credit/Debit card authorization code")
    card_last_four_digits = models.CharField(max_length=4, blank=True, help_text="Last 4 digits of card")
    card_type = models.CharField(max_length=20, blank=True, help_text="Card type (Visa, Mastercard, etc.)")
    payment_verified = models.BooleanField(default=False, help_text="Whether payment has been verified")
    payment_verification_notes = models.TextField(blank=True, help_text="Notes about payment verification")

    # Additional information
    remarks = models.TextField(blank=True, help_text="Additional notes")
    staff_handled_by = models.CharField(max_length=255, blank=True, help_text="Staff member who handled transaction")

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Product Transaction"
        verbose_name_plural = "Product Transactions"

    def __str__(self):
        return f"{self.transaction_id} - {self.get_transaction_type_display()} - {self.product.name}"

    def save(self, *args, **kwargs):
        """Override save to generate transaction ID and update stock"""
        if not self.transaction_id:
            # Generate unique transaction ID
            prefix = 'PT'
            last_transaction = ProductTransaction.objects.filter(
                transaction_id__startswith=prefix
            ).order_by('-transaction_id').first()

            if last_transaction:
                last_number = int(last_transaction.transaction_id[2:])
                new_number = last_number + 1
            else:
                new_number = 1

            self.transaction_id = f"{prefix}{new_number:06d}"

        # Calculate total amount if not provided
        if not self.total_amount:
            self.total_amount = self.quantity * self.unit_price

        # Update product stock based on transaction type
        if self.pk is None:  # New transaction
            if self.transaction_type in ['sale', 'damage']:
                # Decrease stock
                self.product.current_stock -= self.quantity
            elif self.transaction_type in ['purchase', 'return']:
                # Increase stock
                self.product.current_stock += self.quantity
            elif self.transaction_type == 'adjustment':
                # For adjustments, quantity can be positive or negative
                self.product.current_stock += self.quantity

            self.product.save()

        super().save(*args, **kwargs)


# Import unified transaction models
from .models_unified import UnifiedTransaction, UnifiedTransactionLineItem
