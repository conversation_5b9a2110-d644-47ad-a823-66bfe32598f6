{% extends 'base.html' %}
{% load static %}

{% block title %}Transaction {{ transaction.transaction_id }} - REMR Aesthetic Centre{% endblock %}

{% block extra_css %}
<style>
    .transaction-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .status-badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
    
    .line-item-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    
    .line-item-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .payment-info {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 10px;
    }
    
    .summary-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 10px;
    }
    
    @media print {
        .no-print { display: none !important; }
        .transaction-header { background: #f8f9fa !important; color: #333 !important; }
        .payment-info { background: #f8f9fa !important; color: #333 !important; }
        .summary-card { background: #f8f9fa !important; color: #333 !important; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Transaction Header -->
    <div class="transaction-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-receipt me-3"></i>
                    Transaction {{ transaction.transaction_id }}
                </h1>
                <p class="mb-0 opacity-75">
                    <i class="fas fa-calendar me-2"></i>
                    {{ transaction.payment_date|date:"F d, Y g:i A" }}
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <span class="badge status-badge 
                    {% if transaction.payment_status == 'paid' %}bg-success
                    {% elif transaction.payment_status == 'partial' %}bg-warning
                    {% else %}bg-danger{% endif %}">
                    {{ transaction.get_payment_status_display }}
                </span>
                <div class="mt-2">
                    <span class="badge status-badge 
                        {% if transaction.status == 'completed' %}bg-success
                        {% elif transaction.status == 'active' %}bg-primary
                        {% elif transaction.status == 'cancelled' %}bg-danger
                        {% else %}bg-secondary{% endif %}">
                        {{ transaction.get_status_display }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Customer Information -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>Customer Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Name:</strong><br>
                        <span class="text-muted">{{ transaction.customer_name }}</span>
                    </div>
                    {% if transaction.customer_phone %}
                    <div class="mb-3">
                        <strong>Phone:</strong><br>
                        <span class="text-muted">{{ transaction.customer_phone }}</span>
                    </div>
                    {% endif %}
                    {% if transaction.customer_email %}
                    <div class="mb-3">
                        <strong>Email:</strong><br>
                        <span class="text-muted">{{ transaction.customer_email }}</span>
                    </div>
                    {% endif %}
                    {% if transaction.staff_received_by %}
                    <div class="mb-3">
                        <strong>Served by:</strong><br>
                        <span class="text-muted">{{ transaction.staff_received_by }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Payment Information -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header payment-info">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>Payment Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Payment Method:</strong><br>
                        <span class="text-muted">{{ transaction.get_payment_mode_display }}</span>
                    </div>
                    
                    {% if transaction.payment_mode == 'cash' and transaction.cash_amount_received %}
                    <div class="mb-3">
                        <strong>Amount Received:</strong><br>
                        <span class="text-muted">₱{{ transaction.cash_amount_received }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Change:</strong><br>
                        <span class="text-muted">₱{{ transaction.change_amount }}</span>
                    </div>
                    {% endif %}
                    
                    {% if transaction.payment_mode == 'gcash' and transaction.gcash_reference_number %}
                    <div class="mb-3">
                        <strong>GCash Reference:</strong><br>
                        <span class="text-muted">{{ transaction.gcash_reference_number }}</span>
                    </div>
                    {% endif %}
                    
                    {% if transaction.voucher_codes_used %}
                    <div class="mb-3">
                        <strong>Discounts Applied:</strong><br>
                        <span class="text-muted">{{ transaction.voucher_codes_used }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Transaction Summary -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header summary-card">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>Transaction Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span>₱{{ transaction.subtotal }}</span>
                    </div>
                    {% if transaction.discount_percent > 0 %}
                    <div class="d-flex justify-content-between mb-2">
                        <span>Manual Discount ({{ transaction.discount_percent }}%):</span>
                        <span>-₱{{ transaction.subtotal|floatformat:2 }}</span>
                    </div>
                    {% endif %}
                    {% if transaction.voucher_discount > 0 %}
                    <div class="d-flex justify-content-between mb-2">
                        <span>Additional Discounts:</span>
                        <span>-₱{{ transaction.voucher_discount }}</span>
                    </div>
                    {% endif %}
                    {% if transaction.discount_amount > 0 %}
                    <div class="d-flex justify-content-between mb-2 text-warning">
                        <strong>Total Discount:</strong>
                        <strong>-₱{{ transaction.discount_amount }}</strong>
                    </div>
                    {% endif %}
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong>Total Amount:</strong>
                        <strong class="text-success">₱{{ transaction.total_amount }}</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Line Items -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Items & Services
                        <span class="badge bg-light text-dark ms-2">{{ transaction.line_items_count }} items</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% for item in transaction.line_items.all %}
                    <div class="line-item-card p-3 {% if not forloop.last %}border-bottom{% endif %}">
                        <div class="row align-items-center">
                            <div class="col-md-1 text-center">
                                {% if item.item_type == 'product' %}
                                    <i class="fas fa-box text-primary fa-2x"></i>
                                {% else %}
                                    <i class="fas fa-spa text-success fa-2x"></i>
                                {% endif %}
                            </div>
                            <div class="col-md-5">
                                <h6 class="mb-1">{{ item.item_name }}</h6>
                                <small class="text-muted">
                                    {% if item.item_type == 'product' %}
                                        Product
                                    {% else %}
                                        Service
                                    {% endif %}
                                </small>
                            </div>
                            <div class="col-md-2 text-center">
                                <strong>₱{{ item.unit_price }}</strong><br>
                                <small class="text-muted">per {% if item.item_type == 'product' %}unit{% else %}session{% endif %}</small>
                            </div>
                            <div class="col-md-2 text-center">
                                <strong>{{ item.quantity|default:item.sessions }}</strong><br>
                                <small class="text-muted">{% if item.item_type == 'product' %}qty{% else %}sessions{% endif %}</small>
                            </div>
                            <div class="col-md-2 text-end">
                                <strong class="text-success">₱{{ item.line_total }}</strong>
                            </div>
                        </div>
                        
                        {% if item.item_type == 'service' and item.sessions_remaining > 0 %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>{{ item.sessions_remaining }}</strong> session(s) remaining
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% empty %}
                    <div class="p-4 text-center text-muted">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>No items found for this transaction.</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Remarks -->
    {% if transaction.remarks %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>Additional Notes
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ transaction.remarks }}</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Customer Attachments -->
    {% if transaction.attachment_1 or transaction.attachment_2 or transaction.attachment_3 %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-paperclip me-2"></i>Customer Documents</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if transaction.attachment_1 %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary h-100">
                                <div class="card-body text-center">
                                    {% if transaction.attachment_1.url|slice:"-4:" == ".jpg" or transaction.attachment_1.url|slice:"-4:" == ".png" or transaction.attachment_1.url|slice:"-5:" == ".jpeg" or transaction.attachment_1.url|slice:"-5:" == ".webp" or transaction.attachment_1.url|slice:"-4:" == ".gif" %}
                                        <div class="mb-2">
                                            <img src="{{ transaction.attachment_1.url }}" alt="{{ transaction.attachment_1_description }}" class="img-thumbnail" style="max-height: 150px; max-width: 100%;">
                                        </div>
                                    {% else %}
                                        <div class="mb-2">
                                            <i class="fas fa-file-alt fa-3x text-primary"></i>
                                        </div>
                                    {% endif %}
                                    <h6 class="card-title text-primary">{{ transaction.attachment_1_description|default:"Document 1" }}</h6>
                                    <p class="card-text small text-muted">
                                        <i class="fas fa-file me-1"></i>{{ transaction.attachment_1.name|slice:"-25:" }}
                                    </p>
                                    <div class="btn-group" role="group">
                                        <a href="{{ transaction.attachment_1.url }}" target="_blank" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                        <a href="{{ transaction.attachment_1.url }}" download class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-download me-1"></i>Download
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if transaction.attachment_2 %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary h-100">
                                <div class="card-body text-center">
                                    {% if transaction.attachment_2.url|slice:"-4:" == ".jpg" or transaction.attachment_2.url|slice:"-4:" == ".png" or transaction.attachment_2.url|slice:"-5:" == ".jpeg" or transaction.attachment_2.url|slice:"-5:" == ".webp" or transaction.attachment_2.url|slice:"-4:" == ".gif" %}
                                        <div class="mb-2">
                                            <img src="{{ transaction.attachment_2.url }}" alt="{{ transaction.attachment_2_description }}" class="img-thumbnail" style="max-height: 150px; max-width: 100%;">
                                        </div>
                                    {% else %}
                                        <div class="mb-2">
                                            <i class="fas fa-file-alt fa-3x text-primary"></i>
                                        </div>
                                    {% endif %}
                                    <h6 class="card-title text-primary">{{ transaction.attachment_2_description|default:"Document 2" }}</h6>
                                    <p class="card-text small text-muted">
                                        <i class="fas fa-file me-1"></i>{{ transaction.attachment_2.name|slice:"-25:" }}
                                    </p>
                                    <div class="btn-group" role="group">
                                        <a href="{{ transaction.attachment_2.url }}" target="_blank" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                        <a href="{{ transaction.attachment_2.url }}" download class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-download me-1"></i>Download
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if transaction.attachment_3 %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary h-100">
                                <div class="card-body text-center">
                                    {% if transaction.attachment_3.url|slice:"-4:" == ".jpg" or transaction.attachment_3.url|slice:"-4:" == ".png" or transaction.attachment_3.url|slice:"-5:" == ".jpeg" or transaction.attachment_3.url|slice:"-5:" == ".webp" or transaction.attachment_3.url|slice:"-4:" == ".gif" %}
                                        <div class="mb-2">
                                            <img src="{{ transaction.attachment_3.url }}" alt="{{ transaction.attachment_3_description }}" class="img-thumbnail" style="max-height: 150px; max-width: 100%;">
                                        </div>
                                    {% else %}
                                        <div class="mb-2">
                                            <i class="fas fa-file-alt fa-3x text-primary"></i>
                                        </div>
                                    {% endif %}
                                    <h6 class="card-title text-primary">{{ transaction.attachment_3_description|default:"Document 3" }}</h6>
                                    <p class="card-text small text-muted">
                                        <i class="fas fa-file me-1"></i>{{ transaction.attachment_3.name|slice:"-25:" }}
                                    </p>
                                    <div class="btn-group" role="group">
                                        <a href="{{ transaction.attachment_3.url }}" target="_blank" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                        <a href="{{ transaction.attachment_3.url }}" download class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-download me-1"></i>Download
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="row mt-4 no-print">
        <div class="col-12 text-center">
            <a href="{% url 'unified_transaction_list' %}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Transactions
            </a>
            <button onclick="window.print()" class="btn btn-primary me-2">
                <i class="fas fa-print me-2"></i>Print Receipt
            </button>
            <a href="{% url 'unified_transaction_history' transaction.pk %}" class="btn btn-info me-2">
                <i class="fas fa-history me-2"></i>View History
            </a>
            {% if transaction.status == 'active' %}
            <a href="{% url 'unified_transaction_edit' transaction.pk %}" class="btn btn-warning me-2">
                <i class="fas fa-edit me-2"></i>Edit Transaction
            </a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on print button for quick printing
    const printBtn = document.querySelector('button[onclick="window.print()"]');
    if (printBtn) {
        printBtn.focus();
    }
});
</script>
{% endblock %}
