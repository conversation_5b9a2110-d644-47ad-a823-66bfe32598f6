from django.core.management.base import BaseCommand
from django.db import transaction
from inventory.models import Customer
from inventory.models_unified import UnifiedTransaction


class Command(BaseCommand):
    help = 'Populate Customer model from existing UnifiedTransaction data'

    def handle(self, *args, **options):
        self.stdout.write('Starting customer population from transactions...')
        
        # Get all unique customer names from transactions
        customer_names = UnifiedTransaction.objects.values_list(
            'customer_name', flat=True
        ).distinct().exclude(customer_name__isnull=True).exclude(customer_name='')
        
        created_count = 0
        updated_count = 0
        
        with transaction.atomic():
            for customer_name in customer_names:
                if customer_name:
                    # Get customer's transaction data for additional info
                    customer_transactions = UnifiedTransaction.objects.filter(
                        customer_name=customer_name
                    ).order_by('created_at')
                    
                    first_transaction = customer_transactions.first()
                    last_transaction = customer_transactions.last()
                    total_spent = sum(t.total_amount for t in customer_transactions)
                    transaction_count = customer_transactions.count()
                    
                    # Create or update customer
                    customer, created = Customer.objects.get_or_create(
                        name=customer_name,
                        defaults={
                            'phone': '',  # We don't have phone data from transactions
                            'email': '',  # We don't have email data from transactions
                            'address': '',  # We don't have address data from transactions
                            'medical_notes': f'Auto-created from {transaction_count} transactions. '
                                           f'Total spent: ₱{total_spent}. '
                                           f'First visit: {first_transaction.created_at.date()}. '
                                           f'Last visit: {last_transaction.created_at.date()}.'
                        }
                    )
                    
                    if created:
                        created_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(f'Created customer: {customer_name}')
                        )
                    else:
                        # Update medical_notes with transaction summary if customer already exists
                        customer.medical_notes = f'Updated from {transaction_count} transactions. ' \
                                               f'Total spent: ₱{total_spent}. ' \
                                               f'First visit: {first_transaction.created_at.date()}. ' \
                                               f'Last visit: {last_transaction.created_at.date()}.'
                        customer.save()
                        updated_count += 1
                        self.stdout.write(
                            self.style.WARNING(f'Updated customer: {customer_name}')
                        )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Customer population completed! '
                f'Created: {created_count}, Updated: {updated_count}'
            )
        )
