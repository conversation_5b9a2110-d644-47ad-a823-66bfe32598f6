# REMR Aesthetic Centre POS System

A comprehensive Point of Sale (POS) system designed specifically for REMR Aesthetic Centre by Oriskin, featuring integrated inventory management for both aesthetic services and skincare products.

## Features

### 🏥 Aesthetic Services Management
- **Service Categories**: Facial Treatment, Gluta Treatment, IPL Treatment, Aesthetic Procedures, Skincare Treatments
- **Session Tracking**: Track purchased sessions vs. consumed sessions
- **Service Packages**: Support for multi-session packages with expiry dates
- **Pricing Management**: Flexible pricing per session

### 🛍️ Product Inventory
- **Product Categories**: Skincare Products, Supplements & Vitamins, Cosmetics & Beauty, Equipment & Tools, Treatment Kits, Serums & Treatments, Masks & Peels
- **Stock Management**: Real-time inventory tracking with low stock alerts
- **SKU Management**: Unique product identification and tracking
- **Pricing**: Cost price and selling price management with profit margin calculation

### 💰 Unified POS System
- **Mixed Transactions**: Single transaction can include both products and services
- **Shopping Cart**: Add/remove items, edit quantities, apply discounts
- **Payment Methods**: Cash and GCash payments only (as per business requirements)
- **Discount System**: 1-10% discount range for entire cart
- **Receipt Generation**: Professional receipts with transaction details

### 📊 Analytics & Reporting
- **Dashboard Analytics**: Real-time overview of sales, inventory, and performance
- **Sales Reports**: Comprehensive reporting with category breakdowns
- **Popular Items**: Track best-selling products and services
- **Revenue Tracking**: Daily, weekly, and monthly revenue analysis
- **Payment Method Analysis**: Breakdown by payment type

### 🔍 Advanced Features
- **Category-Based Filtering**: Quick access to services by category tabs
- **Search Functionality**: Real-time search for products and services
- **Stock Validation**: Prevents overselling with real-time stock checks
- **Transaction History**: Complete audit trail of all transactions
- **Customer Management**: Basic customer information tracking

## System Architecture

### Database Models
- **UnifiedTransaction**: Main transaction model supporting both products and services
- **UnifiedTransactionLineItem**: Individual line items within transactions
- **Product**: Product inventory with stock management
- **Service**: Service catalog with session-based pricing
- **Customer**: Customer information management

### Key Components
1. **Shopping Cart System**: Session-based cart management
2. **Inventory Tracking**: Automatic stock deduction for products
3. **Session Management**: Track service sessions purchased vs. used
4. **Payment Processing**: Cash and GCash payment handling
5. **Reporting Engine**: Comprehensive analytics and reporting

## Data Import

The system includes a management command to import services and products from Excel files:

```bash
python manage.py import_excel_services --json-file extracted_services.json
```

This command processes the Excel data and categorizes items appropriately:
- **Services**: Facial treatments, aesthetic procedures, IPL treatments, etc.
- **Products**: Skincare items, supplements, treatment kits from "TAKE HOME MEDS" sheet

## Navigation Structure

### Main Sections
1. **POS System**: Direct access to transaction creation
2. **Transactions**: Dashboard, transaction list, sales reports
3. **Service Management**: Service catalog and history
4. **Product Inventory**: Product management and stock control

### Quick Access
- **Dashboard**: `/inventory/unified/dashboard/` - Main analytics overview
- **New Transaction**: `/inventory/unified/create/` - POS interface
- **Sales Report**: `/inventory/unified/reports/` - Comprehensive sales analysis

## Payment Flow

### Cash Payments
1. Select items and add to cart
2. Apply discount if applicable
3. Choose "Cash" payment method
4. Enter amount received
5. System calculates change automatically

### GCash Payments
1. Select items and add to cart
2. Apply discount if applicable
3. Choose "GCash" payment method
4. Enter GCash reference number for verification
5. Complete transaction

## Service Categories (From Excel Import)

- **FACIAL**: Facial treatments and skincare procedures
- **GLUTA**: Glutathione treatments and whitening services
- **MESO**: Mesotherapy and aesthetic injections
- **OPT**: IPL (Intense Pulsed Light) treatments
- **RF**: Radio frequency treatments
- **HIFUWARTS**: HIFU and wart removal procedures
- **CARBON LASER**: Carbon laser treatments
- **INVASIVE**: Invasive aesthetic procedures
- **PACKAGES**: Service packages and bundles

## Technical Requirements

- Django 5.1.6
- SQLite database (as per business preference)
- Bootstrap 5.3.0 for UI
- Font Awesome 6.4.0 for icons
- DataTables for advanced table functionality

## Business Rules

1. **Payment Methods**: Only Cash and GCash (no bank transfers or credit cards)
2. **Discount Range**: 1-10% maximum discount on entire cart
3. **Stock Management**: Real-time inventory tracking for products
4. **Session Tracking**: Services track sessions purchased vs. consumed
5. **No Medicine Functionality**: System focuses exclusively on aesthetic services

## Getting Started

1. Run migrations: `python manage.py migrate`
2. Import Excel data: `python manage.py import_excel_services`
3. Create superuser: `python manage.py createsuperuser`
4. Start server: `python manage.py runserver`
5. Access POS: Navigate to `/inventory/unified/dashboard/`

## Support

For technical support or feature requests, contact the development team.

---

**REMR Aesthetic Centre by Oriskin** - Professional aesthetic services with comprehensive POS management.
