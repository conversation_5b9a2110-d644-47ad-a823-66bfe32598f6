from django.core.management.base import BaseCommand
from django.db.models import Max, Q
from django.db import IntegrityError
from inventory.models_unified import UnifiedTransaction
from user_management.models import Customer
from django.utils import timezone


class Command(BaseCommand):
    help = 'Sync customers from UnifiedTransaction to Customer management system'

    def handle(self, *args, **options):
        self.stdout.write('Syncing customers from unified transactions...')
        
        # Get unique customers from unified transactions
        unique_customers = UnifiedTransaction.objects.values(
            'customer_name', 'customer_phone', 'customer_email'
        ).distinct()
        
        # Track statistics
        total_imported = 0
        already_exists = 0
        errors = 0
        
        for customer_data in unique_customers:
            customer_name = customer_data['customer_name']
            customer_phone = customer_data['customer_phone']
            customer_email = customer_data['customer_email']
            
            if not customer_name:
                continue
            
            # Check if customer already exists
            existing_customer = Customer.objects.filter(
                customer_name=customer_name
            ).first()
            
            if existing_customer:
                # Update existing customer with latest info
                if customer_phone and not existing_customer.phone_number:
                    existing_customer.phone_number = customer_phone
                if customer_email and not existing_customer.email:
                    existing_customer.email = customer_email
                
                # Update last transaction date
                latest_transaction = UnifiedTransaction.objects.filter(
                    customer_name=customer_name
                ).order_by('-payment_date').first()
                
                if latest_transaction:
                    existing_customer.last_transaction_date = latest_transaction.payment_date
                
                existing_customer.save()
                already_exists += 1
                continue
            
            # Get latest transaction date for this customer
            latest_transaction = UnifiedTransaction.objects.filter(
                customer_name=customer_name
            ).order_by('-payment_date').first()
            
            # Generate unique patient number
            patient_number = self.generate_patient_number()
            
            try:
                # Create new customer
                Customer.objects.create(
                    customer_name=customer_name,
                    patient_number=patient_number,
                    customer_type='out_patient',  # Default for aesthetic centre
                    phone_number=customer_phone or '',
                    email=customer_email or '',
                    last_transaction_date=latest_transaction.payment_date if latest_transaction else timezone.now(),
                    notes=f'Auto-imported from unified transactions on {timezone.now().strftime("%Y-%m-%d")}'
                )
                total_imported += 1
                self.stdout.write(f'✓ Imported: {customer_name}')
                
            except IntegrityError as e:
                # Handle duplicate patient numbers
                try:
                    # Try with a different patient number
                    patient_number = self.generate_patient_number(suffix=total_imported)
                    Customer.objects.create(
                        customer_name=customer_name,
                        patient_number=patient_number,
                        customer_type='out_patient',
                        phone_number=customer_phone or '',
                        email=customer_email or '',
                        last_transaction_date=latest_transaction.payment_date if latest_transaction else timezone.now(),
                        notes=f'Auto-imported from unified transactions on {timezone.now().strftime("%Y-%m-%d")}'
                    )
                    total_imported += 1
                    self.stdout.write(f'✓ Imported: {customer_name} (with modified patient number)')
                    
                except Exception as inner_e:
                    self.stdout.write(f'✗ Failed to import {customer_name}: {str(inner_e)}')
                    errors += 1
            
            except Exception as e:
                self.stdout.write(f'✗ Failed to import {customer_name}: {str(e)}')
                errors += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nSync completed!\n'
                f'✓ Imported: {total_imported} new customers\n'
                f'✓ Updated: {already_exists} existing customers\n'
                f'✗ Errors: {errors}'
            )
        )
    
    def generate_patient_number(self, suffix=None):
        """Generate a unique patient number"""
        import random
        import string
        
        # Format: REMR-YYYYMMDD-XXXX
        date_part = timezone.now().strftime('%Y%m%d')
        random_part = ''.join(random.choices(string.digits, k=4))
        
        if suffix:
            random_part = f"{random_part}{suffix:02d}"[-4:]  # Keep only last 4 digits
        
        return f"REMR-{date_part}-{random_part}"
