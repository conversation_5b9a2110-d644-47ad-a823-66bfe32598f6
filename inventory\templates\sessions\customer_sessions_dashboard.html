{% extends 'base.html' %}
{% load static %}

{% block title %}Customer Sessions Dashboard{% endblock %}

{% block extra_css %}
<style>
    .session-card {
        transition: transform 0.2s;
    }
    .session-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .sessions-remaining {
        font-size: 1.2em;
        font-weight: bold;
    }
    .service-item {
        border-left: 4px solid #007bff;
        padding-left: 15px;
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-calendar-check text-primary"></i> Customer Sessions Dashboard</h2>
                    <p class="text-muted">Track and manage customer service sessions</p>
                </div>
                <div class="text-end">
                    <a href="{% url 'session_calendar' %}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-calendar-alt"></i> Calendar View
                    </a>
                    <a href="{% url 'session_reports' %}" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar"></i> Reports
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ total_customers }}</h4>
                            <p class="card-text">Active Customers</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ total_sessions_remaining }}</h4>
                            <p class="card-text">Total Sessions Remaining</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ customers_with_sessions|length }}</h4>
                            <p class="card-text">Services with Sessions</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-spa fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Sessions List -->
    <div class="row">
        {% for customer in customers_with_sessions %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card session-card h-100">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">{{ customer.customer_name }}</h5>
                            {% if customer.customer_phone %}
                                <small class="text-muted">{{ customer.customer_phone }}</small>
                            {% endif %}
                        </div>
                        <div class="text-end">
                            <span class="badge bg-primary sessions-remaining">
                                {{ customer.total_sessions_remaining }} sessions
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% for service in customer.services %}
                    <div class="service-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ service.service_name }}</h6>
                                <small class="text-muted">
                                    Transaction: {{ service.transaction_id }}<br>
                                    Purchased: {{ service.purchase_date|date:"M d, Y" }}
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-success">{{ service.sessions_remaining }} left</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <a href="{% url 'customer_session_detail' service.line_item.id %}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> View Details
                            </a>
                            <button class="btn btn-sm btn-success" 
                                    onclick="quickConsumeSession({{ service.line_item.id }}, '{{ service.service_name }}')">
                                <i class="fas fa-check"></i> Complete Session
                            </button>
                        </div>
                    </div>
                    {% if not forloop.last %}<hr class="my-2">{% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No Active Sessions</h4>
                    <p class="text-muted">No customers have remaining sessions at this time.</p>
                    <a href="{% url 'unified_transaction_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create New Transaction
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Quick Session Consumption Modal -->
<div class="modal fade" id="quickSessionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Complete Session</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickSessionForm">
                    <div class="mb-3">
                        <label class="form-label">Service</label>
                        <input type="text" class="form-control" id="serviceName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Staff Performed By</label>
                        <input type="text" class="form-control" id="staffPerformedBy" 
                               placeholder="Enter staff member name">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Duration (minutes)</label>
                        <input type="number" class="form-control" id="durationMinutes" 
                               placeholder="Session duration">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Session Notes</label>
                        <textarea class="form-control" id="sessionNotes" rows="3" 
                                  placeholder="Session notes or observations..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Customer Feedback</label>
                        <textarea class="form-control" id="customerFeedback" rows="2" 
                                  placeholder="Customer feedback or comments..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="submitQuickSession()">
                    <i class="fas fa-check"></i> Complete Session
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentLineItemId = null;

function quickConsumeSession(lineItemId, serviceName) {
    currentLineItemId = lineItemId;
    document.getElementById('serviceName').value = serviceName;
    
    // Clear form
    document.getElementById('staffPerformedBy').value = '';
    document.getElementById('durationMinutes').value = '';
    document.getElementById('sessionNotes').value = '';
    document.getElementById('customerFeedback').value = '';
    
    // Show modal
    new bootstrap.Modal(document.getElementById('quickSessionModal')).show();
}

function submitQuickSession() {
    if (!currentLineItemId) return;
    
    const formData = {
        staff_performed_by: document.getElementById('staffPerformedBy').value,
        duration_minutes: document.getElementById('durationMinutes').value || null,
        notes: document.getElementById('sessionNotes').value,
        customer_feedback: document.getElementById('customerFeedback').value
    };
    
    fetch(`/sessions/consume/${currentLineItemId}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('quickSessionModal')).hide();
            
            // Show success message
            alert(data.message);
            
            // Reload page to update counts
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while completing the session.');
    });
}
</script>
{% endblock %}
