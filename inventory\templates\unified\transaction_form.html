{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Enhanced Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>{{ page_title }}
                </h2>
                <div class="badge bg-light text-primary border px-3 py-2">
                    <i class="fas fa-clock me-1"></i><span id="current-datetime"></span>
                </div>
            </div>
            <p class="text-muted mb-0">Create transactions with products and services</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <div class="row">
        <!-- Shopping Cart Column -->
        <div class="col-lg-4 order-lg-2">
            <div class="card shadow-sm border-0 rounded-3 mb-4 sticky-top" style="top: 20px;">
                <div class="card-header" style="background: linear-gradient(135deg, #8B7355 0%, #A0956B 100%); color: white;">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>Shopping Cart
                        <span id="cart-count" class="badge bg-light text-dark ms-2">0</span>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Add Item Section -->
                    <div class="mb-4">
                        <h6 class="text-primary border-bottom pb-2 mb-3">Add Items</h6>
                        
                        <!-- Item Type Selection -->
                        <div class="mb-3">
                            <label class="form-label fw-medium">Item Type</label>
                            <select class="form-select" id="item-type-select">
                                <option value="product">Product</option>
                                <option value="service">Service</option>
                            </select>
                        </div>

                        <!-- Product Selection -->
                        <div id="product-selection" class="item-selection">
                            <!-- Product Filters -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label fw-medium">Category Filter</label>
                                    <select class="form-select" id="product-category-filter">
                                        <option value="">All Categories</option>
                                        <option value="skincare">Skincare Products</option>
                                        <option value="supplements">Supplements & Vitamins</option>
                                        <option value="cosmetics">Cosmetics & Beauty</option>
                                        <option value="equipment">Equipment & Tools</option>
                                        <option value="treatment_kits">Treatment Kits</option>
                                        <option value="serums">Serums & Treatments</option>
                                        <option value="masks">Masks & Peels</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-medium">Search Products</label>
                                    <input type="text" class="form-control" id="product-search" placeholder="Type to search...">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-medium">Product</label>
                                <select class="form-select" id="product-select">
                                    <option value="">Select a product</option>
                                </select>
                                <small class="text-muted">Showing <span id="product-count">0</span> products</small>
                            </div>
                            <div class="row">
                                <div class="col-8">
                                    <label class="form-label fw-medium">Quantity</label>
                                    <input type="number" class="form-control" id="product-quantity" min="1" value="1">
                                </div>
                                <div class="col-4">
                                    <label class="form-label fw-medium">&nbsp;</label>
                                    <button type="button" class="btn btn-primary w-100" id="add-product-btn">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="product-info" class="mt-2 p-3 bg-light rounded d-none">
                                <div class="row">
                                    <div class="col-8">
                                        <h6 id="product-name" class="mb-1"></h6>
                                        <p id="product-description" class="text-muted mb-1 small"></p>
                                        <small class="text-muted">Category: <span id="product-category-display"></span></small>
                                    </div>
                                    <div class="col-4 text-end">
                                        <div id="product-pricing">
                                            <div><strong>₱<span id="product-price">0.00</span></strong>/<span id="product-unit">unit</span></div>
                                            <div id="product-promotional-price" class="text-success small" style="display: none;">
                                                <del class="text-muted">₱<span id="product-regular-price">0.00</span></del>
                                                <strong>₱<span id="product-promo-price">0.00</span></strong> <span class="badge bg-success">PROMO</span>
                                            </div>
                                            <div id="product-cash-price" class="text-warning small" style="display: none;">
                                                Cash Price: <strong>₱<span id="product-cash-discount">0.00</span></strong>
                                            </div>
                                        </div>
                                        <div class="text-muted">Stock: <span id="product-stock">0</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Service Selection -->
                        <div id="service-selection" class="item-selection d-none">
                            <!-- Service Category Tabs -->
                            <div class="mb-3">
                                <label class="form-label fw-medium">Service Categories</label>
                                <div class="btn-group w-100" role="group" aria-label="Service categories">
                                    <input type="radio" class="btn-check" name="service-category-tabs" id="tab-all" value="" checked>
                                    <label class="btn btn-outline-primary btn-sm" for="tab-all">All</label>

                                    <input type="radio" class="btn-check" name="service-category-tabs" id="tab-facial" value="facial_treatment">
                                    <label class="btn btn-outline-primary btn-sm" for="tab-facial">Facial</label>

                                    <input type="radio" class="btn-check" name="service-category-tabs" id="tab-gluta" value="gluta_treatment">
                                    <label class="btn btn-outline-primary btn-sm" for="tab-gluta">Gluta</label>

                                    <input type="radio" class="btn-check" name="service-category-tabs" id="tab-ipl" value="ipl_treatment">
                                    <label class="btn btn-outline-primary btn-sm" for="tab-ipl">IPL</label>

                                    <input type="radio" class="btn-check" name="service-category-tabs" id="tab-aesthetic" value="aesthetic_procedure">
                                    <label class="btn btn-outline-primary btn-sm" for="tab-aesthetic">Aesthetic</label>
                                </div>
                                <div class="btn-group w-100 mt-2" role="group">
                                    <input type="radio" class="btn-check" name="service-category-tabs" id="tab-other" value="other">
                                    <label class="btn btn-outline-primary btn-sm" for="tab-other">Other</label>
                                </div>
                            </div>

                            <!-- Service Search -->
                            <div class="mb-3">
                                <label class="form-label fw-medium">Search Services</label>
                                <input type="text" class="form-control" id="service-search" placeholder="Type to search services...">
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-medium">Service</label>
                                <select class="form-select" id="service-select">
                                    <option value="">Select a service</option>
                                </select>
                                <small class="text-muted">Showing <span id="service-count">0</span> services</small>
                            </div>
                            <div class="row">
                                <div class="col-8">
                                    <label class="form-label fw-medium">Sessions</label>
                                    <input type="number" class="form-control" id="service-sessions" min="1" value="1">
                                </div>
                                <div class="col-4">
                                    <label class="form-label fw-medium">&nbsp;</label>
                                    <button type="button" class="btn btn-primary w-100" id="add-service-btn">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="service-info" class="mt-2 p-3 bg-light rounded d-none">
                                <div class="row">
                                    <div class="col-8">
                                        <h6 id="service-name" class="mb-1"></h6>
                                        <p id="service-description" class="text-muted mb-1 small"></p>
                                        <small class="text-muted">Category: <span id="service-category-display"></span></small>
                                    </div>
                                    <div class="col-4 text-end">
                                        <div id="service-pricing">
                                            <div><strong>₱<span id="service-price">0.00</span></strong>/session</div>
                                            <div id="service-promotional-price" class="text-success small" style="display: none;">
                                                <del class="text-muted">₱<span id="service-regular-price">0.00</span></del>
                                                <strong>₱<span id="service-promo-price">0.00</span></strong> <span class="badge bg-success">PROMO</span>
                                            </div>
                                            <div id="service-cash-price" class="text-warning small" style="display: none;">
                                                Cash Price: <strong>₱<span id="service-cash-discount">0.00</span></strong>
                                            </div>
                                        </div>
                                        <div class="text-muted"><span id="service-duration">0</span> minutes</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cart Items -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="text-primary border-bottom pb-2 mb-0">Cart Items</h6>
                            <button type="button" class="btn btn-outline-danger btn-sm" id="clear-cart-btn">
                                <i class="fas fa-trash me-1"></i>Clear
                            </button>
                        </div>
                        
                        <div id="cart-items">
                            <div id="empty-cart" class="text-center text-muted py-4">
                                <i class="fas fa-shopping-cart fa-2x mb-2 opacity-50"></i>
                                <p class="mb-0">Cart is empty</p>
                                <small>Add products or services above</small>
                            </div>
                        </div>
                    </div>

                    <!-- Cart Totals -->
                    <div class="border-top pt-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span id="cart-subtotal" class="fw-bold">₱0.00</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Discount:</span>
                            <span id="cart-discount" class="text-success">₱0.00</span>
                        </div>
                        <div class="d-flex justify-content-between border-top pt-2">
                            <span class="fw-bold">Total:</span>
                            <span id="cart-total" class="fw-bold text-primary fs-5">₱0.00</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Form Column -->
        <div class="col-lg-8 order-lg-1">
            <div class="card shadow-sm border-0 rounded-3 mb-4">
                <div class="card-header" style="background: linear-gradient(135deg, #8B7355 0%, #A0956B 100%); color: white;">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>Transaction Details
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="post" id="unified-transaction-form">
                        {% csrf_token %}
                        <!-- Hidden fields for voucher data -->
                        <input type="hidden" id="voucher-codes" name="voucher_codes" value="">
                        <input type="hidden" id="voucher-discount-amount" name="voucher_discount" value="0">
                        
                        <!-- Customer Information Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-user me-2"></i>Customer Information
                                </h5>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-medium">
                                    <i class="fas fa-user me-1"></i>{{ form.customer_name.label }}
                                </label>
                                {{ form.customer_name }}
                                {% if form.customer_name.errors %}
                                    <div class="text-danger small">{{ form.customer_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-medium">
                                    <i class="fas fa-phone me-1"></i>{{ form.customer_phone.label }}
                                </label>
                                {{ form.customer_phone }}
                                {% if form.customer_phone.errors %}
                                    <div class="text-danger small">{{ form.customer_phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-medium">
                                    <i class="fas fa-envelope me-1"></i>{{ form.customer_email.label }}
                                </label>
                                {{ form.customer_email }}
                                {% if form.customer_email.errors %}
                                    <div class="text-danger small">{{ form.customer_email.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Payment Information Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-credit-card me-2"></i>Payment Information
                                </h5>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-medium">
                                    <i class="fas fa-percent me-1"></i>{{ form.discount_percent.label }}
                                </label>
                                {{ form.discount_percent }}
                                {% if form.discount_percent.errors %}
                                    <div class="text-danger small">{{ form.discount_percent.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    0% to 10% discount
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-medium">
                                    <i class="fas fa-credit-card me-1"></i>{{ form.payment_mode.label }}
                                </label>
                                {{ form.payment_mode }}
                                {% if form.payment_mode.errors %}
                                    <div class="text-danger small">{{ form.payment_mode.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-medium">
                                    <i class="fas fa-calendar me-1"></i>{{ form.payment_date.label }}
                                </label>
                                {{ form.payment_date }}
                                {% if form.payment_date.errors %}
                                    <div class="text-danger small">{{ form.payment_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Payment Verification Section -->
                        <div id="payment-verification-section" class="row mb-4" style="display: none;">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-shield-alt me-2"></i>Payment Verification
                                </h5>
                            </div>

                            <!-- Cash Payment Fields -->
                            <div id="cash-payment-fields" class="payment-method-fields row" style="display: none;">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-medium">
                                        <i class="fas fa-money-bill me-1"></i>{{ form.cash_amount_received.label }}
                                    </label>
                                    {{ form.cash_amount_received }}
                                    {% if form.cash_amount_received.errors %}
                                        <div class="text-danger small">{{ form.cash_amount_received.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div id="cash-change-display" class="mt-2 p-3 bg-success bg-opacity-10 rounded d-none">
                                        <strong>Change to give:</strong> <span id="change-amount" class="text-success fw-bold">₱0.00</span>
                                    </div>
                                </div>
                            </div>

                            <!-- GCash Payment Fields -->
                            <div id="gcash-payment-fields" class="payment-method-fields row" style="display: none;">
                                <div class="col-md-12 mb-4">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary text-white text-center">
                                            <h6 class="mb-0">
                                                <i class="fas fa-mobile-alt me-2"></i>GCash Payment
                                            </h6>
                                        </div>
                                        <div class="card-body text-center">
                                            <div id="gcash-qr-section">
                                                <div id="gcash-qr-code" class="mb-3">
                                                    <!-- QR code will be displayed here -->
                                                </div>
                                                <button type="button" id="generate-qr-btn" class="btn btn-primary">
                                                    <i class="fas fa-qrcode me-2"></i>Show GCash QR Code
                                                </button>
                                            </div>
                                            <div id="payment-amount-display" class="mt-3 p-3 bg-warning bg-opacity-10 rounded d-none">
                                                <strong>Amount to Pay: <span id="gcash-amount" class="text-primary fw-bold">₱0.00</span></strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-medium">
                                        <i class="fas fa-hashtag me-1"></i>{{ form.gcash_reference_number.label }}
                                    </label>
                                    {{ form.gcash_reference_number }}
                                    {% if form.gcash_reference_number.errors %}
                                        <div class="text-danger small">{{ form.gcash_reference_number.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">
                                        Enter the reference number from your GCash payment confirmation
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Multiple Discount Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-warning border-bottom pb-2 mb-3">
                                    <i class="fas fa-percent me-2"></i>Discounts & Adjustments
                                </h5>
                            </div>

                            <!-- Discount Type Selection -->
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-medium">Discount Type</label>
                                <select class="form-select" id="discount-type">
                                    <option value="percentage">Percentage Discount</option>
                                    <option value="fixed">Fixed Amount Discount</option>
                                    <option value="promotional">Promotional Price</option>
                                    <option value="cash">Cash Discount</option>
                                    <option value="loyalty">Loyalty Discount</option>
                                    <option value="senior">Senior/PWD Discount</option>
                                </select>
                            </div>

                            <!-- Discount Value -->
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-medium">Discount Value</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="discount-symbol">%</span>
                                    <input type="number" class="form-control" id="discount-value" min="0" step="0.1" placeholder="0">
                                </div>
                                <div class="form-text">
                                    <span id="discount-help">Enter percentage (0-100)</span>
                                </div>
                            </div>

                            <!-- Discount Reason -->
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-medium">Reason/Notes</label>
                                <input type="text" class="form-control" id="discount-reason" placeholder="e.g., New customer, Loyalty reward">
                            </div>

                            <!-- Applied Discounts Display -->
                            <div class="col-12">
                                <div id="applied-discounts" class="mb-3" style="display: none;">
                                    <label class="form-label fw-medium">Applied Discounts:</label>
                                    <div id="discount-list" class="d-flex flex-wrap gap-2"></div>
                                </div>

                                <!-- Add Discount Button -->
                                <button type="button" class="btn btn-outline-warning" id="add-discount-btn">
                                    <i class="fas fa-plus me-1"></i>Add Discount
                                </button>

                                <!-- Total Discount Display -->
                                <div class="mt-3 p-3 bg-warning bg-opacity-10 rounded">
                                    <div class="row">
                                        <div class="col-6">
                                            <strong>Total Discount Amount:</strong>
                                        </div>
                                        <div class="col-6 text-end">
                                            <strong>₱<span id="total-discount-display">0.00</span></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Additional Information
                                </h5>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-medium">
                                    <i class="fas fa-user-tie me-1"></i>{{ form.staff_received_by.label }}
                                </label>
                                {{ form.staff_received_by }}
                                {% if form.staff_received_by.errors %}
                                    <div class="text-danger small">{{ form.staff_received_by.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-medium">
                                    <i class="fas fa-sticky-note me-1"></i>{{ form.remarks.label }}
                                </label>
                                {{ form.remarks }}
                                {% if form.remarks.errors %}
                                    <div class="text-danger small">{{ form.remarks.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'unified_transaction_list' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to List
                                    </a>
                                    <button type="submit" class="btn btn-success btn-lg" id="submit-transaction-btn" disabled>
                                        <i class="fas fa-save me-2"></i>Create Transaction
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Product and service data from Django
const productsData = JSON.parse('{{ products_json|escapejs }}');
const servicesData = JSON.parse('{{ services_json|escapejs }}');

// Debug: Log the data
console.log('Products loaded:', productsData.length);
console.log('Services loaded:', servicesData.length);
console.log('Sample service:', servicesData[0]);

// Show alert for debugging
if (servicesData.length === 0) {
    alert('No services data loaded!');
} else {
    console.log('Services by category:');
    const cats = {};
    servicesData.forEach(s => {
        cats[s.category] = (cats[s.category] || 0) + 1;
    });
    console.log(cats);
}

// Initialize cart data
let cartData = {
    items: [],
    subtotal: 0,
    items_count: 0,
    total_quantity: 0,
    is_empty: true
};

// Update current date/time
function updateDateTime() {
    const now = new Date();
    document.getElementById('current-datetime').textContent = now.toLocaleString();
}
updateDateTime();
setInterval(updateDateTime, 1000);

// Load cart data on page load
document.addEventListener('DOMContentLoaded', function() {
    loadCartData();
    populateProductSelect();
    populateServiceSelect();
    setupEventListeners();
});

// Populate product select dropdown with filtering
function populateProductSelect(categoryFilter = '', searchFilter = '') {
    const productSelect = document.getElementById('product-select');
    const productCount = document.getElementById('product-count');
    productSelect.innerHTML = '<option value="">Select a product</option>';

    let filteredProducts = productsData.filter(product => {
        const matchesCategory = !categoryFilter || product.category === categoryFilter;
        const matchesSearch = !searchFilter ||
            product.name.toLowerCase().includes(searchFilter.toLowerCase()) ||
            (product.description && product.description.toLowerCase().includes(searchFilter.toLowerCase()));
        return matchesCategory && matchesSearch;
    });

    filteredProducts.forEach(product => {
        const option = document.createElement('option');
        option.value = product.id;
        option.textContent = `${product.name} - ₱${product.selling_price}/${product.unit}`;
        option.dataset.price = product.selling_price;
        option.dataset.stock = product.current_stock;
        option.dataset.unit = product.unit;
        option.dataset.category = product.category;
        option.dataset.description = product.description || '';

        // Add promotional pricing data
        if (product.promotional_price) {
            option.dataset.promoPrice = product.promotional_price;
            option.dataset.isOnPromo = product.is_on_promotion;
        }
        if (product.cash_discount_price) {
            option.dataset.cashPrice = product.cash_discount_price;
        }

        productSelect.appendChild(option);
    });

    if (productCount) {
        productCount.textContent = filteredProducts.length;
    }
}

// Populate service select dropdown with filtering
function populateServiceSelect(categoryFilter = '', searchFilter = '') {
    console.log('populateServiceSelect called with:', categoryFilter, searchFilter);
    console.log('servicesData length:', servicesData.length);

    const serviceSelect = document.getElementById('service-select');
    const serviceCount = document.getElementById('service-count');
    serviceSelect.innerHTML = '<option value="">Select a service</option>';

    let filteredServices = servicesData.filter(service => {
        const matchesCategory = !categoryFilter || service.category === categoryFilter;
        const matchesSearch = !searchFilter ||
            service.name.toLowerCase().includes(searchFilter.toLowerCase()) ||
            (service.description && service.description.toLowerCase().includes(searchFilter.toLowerCase()));
        return matchesCategory && matchesSearch;
    });

    console.log('Filtered services:', filteredServices.length);

    filteredServices.forEach(service => {
        const option = document.createElement('option');
        option.value = service.id;
        option.textContent = `${service.name} - ₱${service.price_per_session}/session`;
        option.dataset.price = service.price_per_session;
        option.dataset.duration = service.duration_minutes;
        option.dataset.category = service.category;
        option.dataset.description = service.description || '';

        // Add promotional pricing data
        if (service.promotional_price) {
            option.dataset.promoPrice = service.promotional_price;
            option.dataset.isOnPromo = service.is_on_promotion;
        }
        if (service.cash_discount_price) {
            option.dataset.cashPrice = service.cash_discount_price;
        }

        serviceSelect.appendChild(option);
    });

    if (serviceCount) {
        serviceCount.textContent = filteredServices.length;
    }
}

// Setup event listeners
function setupEventListeners() {
    // Item type selection
    document.getElementById('item-type-select').addEventListener('change', function() {
        const itemType = this.value;
        const productSelection = document.getElementById('product-selection');
        const serviceSelection = document.getElementById('service-selection');

        if (itemType === 'product') {
            productSelection.classList.remove('d-none');
            serviceSelection.classList.add('d-none');
        } else {
            productSelection.classList.add('d-none');
            serviceSelection.classList.remove('d-none');
        }
    });

    // Product filtering
    document.getElementById('product-category-filter').addEventListener('change', function() {
        const categoryFilter = this.value;
        const searchFilter = document.getElementById('product-search').value;
        populateProductSelect(categoryFilter, searchFilter);
    });

    document.getElementById('product-search').addEventListener('input', function() {
        const searchFilter = this.value;
        const categoryFilter = document.getElementById('product-category-filter').value;
        populateProductSelect(categoryFilter, searchFilter);
    });

    // Service category tabs
    document.querySelectorAll('input[name="service-category-tabs"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                const categoryFilter = this.value;
                const searchFilter = document.getElementById('service-search').value;
                populateServiceSelect(categoryFilter, searchFilter);
            }
        });
    });

    document.getElementById('service-search').addEventListener('input', function() {
        const searchFilter = this.value;
        const selectedTab = document.querySelector('input[name="service-category-tabs"]:checked');
        const categoryFilter = selectedTab ? selectedTab.value : '';
        populateServiceSelect(categoryFilter, searchFilter);
    });

    // Product selection
    document.getElementById('product-select').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const productInfo = document.getElementById('product-info');

        if (selectedOption.value) {
            document.getElementById('product-name').textContent = selectedOption.textContent.split(' - ₱')[0];
            document.getElementById('product-description').textContent = selectedOption.dataset.description || 'No description available';
            document.getElementById('product-category-display').textContent = selectedOption.dataset.category || 'Unknown';

            // Handle pricing display
            const regularPrice = selectedOption.dataset.price;
            const promoPrice = selectedOption.dataset.promoPrice;
            const cashPrice = selectedOption.dataset.cashPrice;
            const isOnPromo = selectedOption.dataset.isOnPromo === 'true';

            // Set main price
            document.getElementById('product-price').textContent = isOnPromo && promoPrice ? promoPrice : regularPrice;
            document.getElementById('product-unit').textContent = selectedOption.dataset.unit;

            // Show/hide promotional pricing
            const promoDiv = document.getElementById('product-promotional-price');
            const cashDiv = document.getElementById('product-cash-price');

            if (promoDiv && isOnPromo && promoPrice && promoPrice !== regularPrice) {
                const regularPriceEl = document.getElementById('product-regular-price');
                const promoPriceEl = document.getElementById('product-promo-price');
                if (regularPriceEl) regularPriceEl.textContent = regularPrice;
                if (promoPriceEl) promoPriceEl.textContent = promoPrice;
                promoDiv.style.display = 'block';
            } else if (promoDiv) {
                promoDiv.style.display = 'none';
            }

            if (cashDiv && cashPrice && cashPrice !== (isOnPromo && promoPrice ? promoPrice : regularPrice)) {
                const cashDiscountEl = document.getElementById('product-cash-discount');
                if (cashDiscountEl) cashDiscountEl.textContent = cashPrice;
                cashDiv.style.display = 'block';
            } else if (cashDiv) {
                cashDiv.style.display = 'none';
            }

            document.getElementById('product-stock').textContent = selectedOption.dataset.stock;
            productInfo.classList.remove('d-none');
        } else {
            productInfo.classList.add('d-none');
        }
    });

    // Service selection
    document.getElementById('service-select').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const serviceInfo = document.getElementById('service-info');

        if (selectedOption.value) {
            document.getElementById('service-name').textContent = selectedOption.textContent.split(' - ₱')[0];
            document.getElementById('service-description').textContent = selectedOption.dataset.description || 'No description available';
            document.getElementById('service-category-display').textContent = selectedOption.dataset.category || 'Unknown';

            // Handle pricing display
            const regularPrice = selectedOption.dataset.price;
            const promoPrice = selectedOption.dataset.promoPrice;
            const cashPrice = selectedOption.dataset.cashPrice;
            const isOnPromo = selectedOption.dataset.isOnPromo === 'true';

            // Set main price
            document.getElementById('service-price').textContent = isOnPromo && promoPrice ? promoPrice : regularPrice;

            // Show/hide promotional pricing
            const promoDiv = document.getElementById('service-promotional-price');
            const cashDiv = document.getElementById('service-cash-price');

            if (promoDiv && isOnPromo && promoPrice && promoPrice !== regularPrice) {
                const regularPriceEl = document.getElementById('service-regular-price');
                const promoPriceEl = document.getElementById('service-promo-price');
                if (regularPriceEl) regularPriceEl.textContent = regularPrice;
                if (promoPriceEl) promoPriceEl.textContent = promoPrice;
                promoDiv.style.display = 'block';
            } else if (promoDiv) {
                promoDiv.style.display = 'none';
            }

            if (cashDiv && cashPrice && cashPrice !== (isOnPromo && promoPrice ? promoPrice : regularPrice)) {
                const cashDiscountEl = document.getElementById('service-cash-discount');
                if (cashDiscountEl) cashDiscountEl.textContent = cashPrice;
                cashDiv.style.display = 'block';
            } else if (cashDiv) {
                cashDiv.style.display = 'none';
            }

            document.getElementById('service-duration').textContent = selectedOption.dataset.duration;
            serviceInfo.classList.remove('d-none');
        } else {
            serviceInfo.classList.add('d-none');
        }
    });

    // Add product button
    document.getElementById('add-product-btn').addEventListener('click', function() {
        const productSelect = document.getElementById('product-select');
        const quantityInput = document.getElementById('product-quantity');

        if (!productSelect.value) {
            alert('Please select a product');
            return;
        }

        const quantity = parseInt(quantityInput.value) || 1;
        if (quantity <= 0) {
            alert('Please enter a valid quantity');
            return;
        }

        addToCart('product', {
            product_id: productSelect.value,
            quantity: quantity
        });
    });

    // Add service button
    document.getElementById('add-service-btn').addEventListener('click', function() {
        const serviceSelect = document.getElementById('service-select');
        const sessionsInput = document.getElementById('service-sessions');

        if (!serviceSelect.value) {
            alert('Please select a service');
            return;
        }

        const sessions = parseInt(sessionsInput.value) || 1;
        if (sessions <= 0) {
            alert('Please enter a valid number of sessions');
            return;
        }

        addToCart('service', {
            service_id: serviceSelect.value,
            sessions: sessions
        });
    });

    // Clear cart button
    document.getElementById('clear-cart-btn').addEventListener('click', function() {
        if (confirm('Are you sure you want to clear the cart?')) {
            clearCart();
        }
    });

    // Discount percentage change
    document.getElementById('id_discount_percent').addEventListener('input', function() {
        updateCartTotals();
    });

    // Discount functionality
    document.getElementById('discount-type').addEventListener('change', function() {
        updateDiscountUI();
    });

    document.getElementById('add-discount-btn').addEventListener('click', function() {
        addDiscount();
    });

    // Initialize discount UI
    updateDiscountUI();

    // Payment mode change
    document.getElementById('id_payment_mode').addEventListener('change', function() {
        const paymentMode = this.value;
        const verificationSection = document.getElementById('payment-verification-section');
        const allPaymentFields = document.querySelectorAll('.payment-method-fields');

        // Hide all payment method fields
        allPaymentFields.forEach(field => {
            if (field) field.style.display = 'none';
        });

        if (paymentMode) {
            if (verificationSection) verificationSection.style.display = 'block';

            // Show relevant payment fields
            const targetFields = document.getElementById(`${paymentMode}-payment-fields`);
            if (targetFields) {
                targetFields.style.display = 'block';
            }
        } else {
            if (verificationSection) verificationSection.style.display = 'none';
        }
    });

    // Cash amount received handler
    document.getElementById('id_cash_amount_received').addEventListener('input', function() {
        const amountReceived = parseFloat(this.value) || 0;
        const totalAmount = getCurrentTotal();
        const changeDisplay = document.getElementById('cash-change-display');
        const changeAmount = document.getElementById('change-amount');

        if (amountReceived > 0 && totalAmount > 0) {
            const change = amountReceived - totalAmount;
            if (change >= 0) {
                changeAmount.textContent = `₱${change.toFixed(2)}`;
                changeDisplay.classList.remove('d-none');
                changeDisplay.className = 'mt-2 p-3 bg-success bg-opacity-10 rounded';
            } else {
                changeAmount.textContent = `₱${Math.abs(change).toFixed(2)} short`;
                changeDisplay.classList.remove('d-none');
                changeDisplay.className = 'mt-2 p-3 bg-danger bg-opacity-10 rounded';
            }
        } else {
            changeDisplay.classList.add('d-none');
        }
    });

    // Generate GCash QR Code
    document.getElementById('generate-qr-btn').addEventListener('click', function() {
        const totalAmount = getCurrentTotal();

        if (totalAmount > 0) {
            const qrCodeSection = document.getElementById('gcash-qr-code');
            const paymentAmountDisplay = document.getElementById('payment-amount-display');
            const gcashAmountSpan = document.getElementById('gcash-amount');
            const button = this;

            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';

            fetch('/generate-gcash-qr/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    amount: totalAmount
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Display the QR code
                    qrCodeSection.innerHTML = `
                        <div class="p-3 bg-white d-inline-block border rounded">
                            <img src="data:image/png;base64,${data.qr_code}" alt="REMR Aesthetic Centre GCash QR" style="width: 200px; height: 200px;">
                            <div class="mt-2">
                                <small class="text-muted fw-bold">${data.merchant_data.merchant_name}</small>
                            </div>
                        </div>
                    `;

                    // Show amount to pay
                    gcashAmountSpan.textContent = `₱${totalAmount.toLocaleString()}`;
                    paymentAmountDisplay.classList.remove('d-none');

                    button.style.display = 'none';
                } else {
                    alert('Error generating QR code: ' + data.error);
                    button.disabled = false;
                    button.innerHTML = '<i class="fas fa-qrcode me-2"></i>Show GCash QR Code';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error generating QR code');
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-qrcode me-2"></i>Show GCash QR Code';
            });
        } else {
            alert('Please add items to cart first');
        }
    });
}

// Cart management functions
function loadCartData() {
    fetch('/cart/data/')
        .then(response => response.json())
        .then(data => {
            cartData = data;
            updateCartDisplay();
        })
        .catch(error => console.error('Error loading cart:', error));
}

function addToCart(itemType, itemData) {
    console.log('Adding to cart:', itemType, itemData);

    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (!csrfToken) {
        console.error('CSRF token not found');
        showMessage('Error: CSRF token not found', 'error');
        return;
    }

    fetch('/cart/add/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken.value
        },
        body: JSON.stringify({
            item_type: itemType,
            ...itemData
        })
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Cart response:', data);
        if (data.success) {
            cartData = data.cart_data;
            updateCartDisplay();

            // Reset form fields
            if (itemType === 'product') {
                document.getElementById('product-select').value = '';
                document.getElementById('product-quantity').value = '1';
                document.getElementById('product-info').classList.add('d-none');
            } else {
                document.getElementById('service-select').value = '';
                document.getElementById('service-sessions').value = '1';
                document.getElementById('service-info').classList.add('d-none');
            }

            // Show success message
            showMessage(data.message, 'success');
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error adding to cart:', error);
        showMessage('Error adding item to cart: ' + error.message, 'error');
    });
}

function updateCartItem(itemKey, quantity) {
    fetch('/inventory/cart/update/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            item_key: itemKey,
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            cartData = data.cart_data;
            updateCartDisplay();
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => console.error('Error updating cart:', error));
}

function removeFromCart(itemKey) {
    console.log('Removing item from cart:', itemKey);

    fetch('/cart/remove/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            item_key: itemKey
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('Cart remove response:', data);
        if (data.success) {
            cartData = data.cart_data;
            updateCartDisplay();
            showMessage('Item removed from cart', 'success');
        } else {
            showMessage('Error removing item: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error removing from cart:', error);
        showMessage('Error removing item from cart', 'error');
    });
}

function clearCart() {
    fetch('/cart/clear/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            cartData = data.cart_data;
            updateCartDisplay();
            showMessage('Cart cleared', 'success');
        }
    })
    .catch(error => console.error('Error clearing cart:', error));
}

function updateCartDisplay() {
    // Update cart count
    document.getElementById('cart-count').textContent = cartData.items_count;

    // Update cart items
    const cartItemsContainer = document.getElementById('cart-items');
    const emptyCart = document.getElementById('empty-cart');

    if (cartData.is_empty) {
        if (emptyCart) emptyCart.style.display = 'block';
        if (cartItemsContainer) {
            cartItemsContainer.innerHTML = '';
            if (emptyCart) cartItemsContainer.appendChild(emptyCart);
        }
    } else {
        if (emptyCart) emptyCart.style.display = 'none';
        if (cartItemsContainer) cartItemsContainer.innerHTML = '';

        cartData.items.forEach(item => {
            const itemElement = createCartItemElement(item);
            cartItemsContainer.appendChild(itemElement);
        });
    }

    // Update totals
    updateCartTotals();

    // Update submit button state
    const submitBtn = document.getElementById('submit-transaction-btn');
    submitBtn.disabled = cartData.is_empty;
}

function createCartItemElement(item) {
    const div = document.createElement('div');
    div.className = 'cart-item border rounded p-3 mb-2';

    const quantityLabel = item.item_type === 'product' ? 'Qty' : 'Sessions';
    const quantityValue = item.item_type === 'product' ? item.quantity : item.sessions;

    div.innerHTML = `
        <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
                <h6 class="mb-1">${item.name}</h6>
                <small class="text-muted">
                    ${item.item_type === 'product' ? 'Product' : 'Service'} • ₱${parseFloat(item.unit_price).toFixed(2)}/${item.item_type === 'product' ? item.unit : 'session'}
                </small>
                <div class="mt-2">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <label class="form-label small mb-1">${quantityLabel}:</label>
                            <input type="number" class="form-control form-control-sm quantity-input"
                                   value="${quantityValue}" min="1" data-item-key="${item.key}">
                        </div>
                        <div class="col-6 text-end">
                            <div class="small text-muted">Total:</div>
                            <div class="fw-bold text-primary">₱${parseFloat(item.total).toFixed(2)}</div>
                        </div>
                    </div>
                </div>
            </div>
            <button type="button" class="btn btn-outline-danger btn-sm ms-2 remove-item-btn" data-item-key="${item.key}">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

    // Add event listeners
    const quantityInput = div.querySelector('.quantity-input');
    quantityInput.addEventListener('change', function() {
        const newQuantity = parseInt(this.value) || 1;
        updateCartItem(this.dataset.itemKey, newQuantity);
    });

    const removeBtn = div.querySelector('.remove-item-btn');
    removeBtn.addEventListener('click', function() {
        if (confirm('Remove this item from cart?')) {
            removeFromCart(this.dataset.itemKey);
        }
    });

    return div;
}

// Multiple discount management
let appliedDiscounts = [];
let totalDiscountAmount = 0;

function updateDiscountUI() {
    const discountType = document.getElementById('discount-type').value;
    const discountSymbol = document.getElementById('discount-symbol');
    const discountHelp = document.getElementById('discount-help');
    const discountValue = document.getElementById('discount-value');

    switch(discountType) {
        case 'percentage':
            discountSymbol.textContent = '%';
            discountHelp.textContent = 'Enter percentage (0-100)';
            discountValue.max = '100';
            discountValue.step = '0.1';
            break;
        case 'fixed':
            discountSymbol.textContent = '₱';
            discountHelp.textContent = 'Enter fixed amount';
            discountValue.max = '';
            discountValue.step = '0.01';
            break;
        case 'promotional':
            discountSymbol.textContent = '₱';
            discountHelp.textContent = 'Enter promotional price';
            discountValue.max = '';
            discountValue.step = '0.01';
            break;
        case 'cash':
            discountSymbol.textContent = '%';
            discountHelp.textContent = 'Cash discount percentage (0-20)';
            discountValue.max = '20';
            discountValue.step = '0.1';
            break;
        case 'loyalty':
            discountSymbol.textContent = '%';
            discountHelp.textContent = 'Loyalty discount percentage (0-15)';
            discountValue.max = '15';
            discountValue.step = '0.1';
            break;
        case 'senior':
            discountSymbol.textContent = '%';
            discountHelp.textContent = 'Senior/PWD discount (20%)';
            discountValue.value = '20';
            discountValue.max = '20';
            discountValue.step = '0.1';
            break;
    }
}

function addDiscount() {
    const discountType = document.getElementById('discount-type').value;
    const discountValue = parseFloat(document.getElementById('discount-value').value) || 0;
    const discountReason = document.getElementById('discount-reason').value.trim();

    if (discountValue <= 0) {
        showMessage('Please enter a valid discount value', 'error');
        return;
    }

    // Calculate discount amount
    let discountAmount = 0;
    const subtotal = cartData.subtotal;

    switch(discountType) {
        case 'percentage':
        case 'cash':
        case 'loyalty':
        case 'senior':
            discountAmount = subtotal * (discountValue / 100);
            break;
        case 'fixed':
            discountAmount = Math.min(discountValue, subtotal);
            break;
        case 'promotional':
            discountAmount = Math.max(0, subtotal - discountValue);
            break;
    }

    // Add to applied discounts
    const discount = {
        id: Date.now(),
        type: discountType,
        value: discountValue,
        amount: discountAmount,
        reason: discountReason || getDiscountTypeName(discountType)
    };

    appliedDiscounts.push(discount);
    totalDiscountAmount = appliedDiscounts.reduce((total, d) => total + d.amount, 0);

    // Update display
    updateDiscountDisplay();
    updateCartTotals();

    // Clear inputs (but don't reset to 0 for senior discount)
    if (discountType !== 'senior') {
        document.getElementById('discount-value').value = '';
    }
    document.getElementById('discount-reason').value = '';

    showMessage(`${getDiscountTypeName(discountType)} applied: ₱${discountAmount.toFixed(2)}`, 'success');
}

function removeDiscount(discountId) {
    appliedDiscounts = appliedDiscounts.filter(d => d.id !== discountId);
    totalDiscountAmount = appliedDiscounts.reduce((total, d) => total + d.amount, 0);
    updateDiscountDisplay();
    updateCartTotals();
    showMessage('Discount removed', 'success');
}

function getDiscountTypeName(type) {
    const names = {
        'percentage': 'Percentage Discount',
        'fixed': 'Fixed Amount Discount',
        'promotional': 'Promotional Price',
        'cash': 'Cash Discount',
        'loyalty': 'Loyalty Discount',
        'senior': 'Senior/PWD Discount'
    };
    return names[type] || type;
}

function updateDiscountDisplay() {
    const discountList = document.getElementById('discount-list');
    const appliedDiscountsSection = document.getElementById('applied-discounts');
    const totalDiscountDisplay = document.getElementById('total-discount-display');

    // Update total discount display
    totalDiscountDisplay.textContent = totalDiscountAmount.toFixed(2);

    if (appliedDiscounts.length > 0) {
        if (appliedDiscountsSection) appliedDiscountsSection.style.display = 'block';
        if (discountList) {
            discountList.innerHTML = '';

            appliedDiscounts.forEach(discount => {
                const discountTag = document.createElement('span');
                discountTag.className = 'badge bg-warning text-dark d-flex align-items-center gap-2';
                discountTag.innerHTML = `
                    <span>${discount.reason} - ₱${discount.amount.toFixed(2)}</span>
                    <button type="button" class="btn-close btn-close-white" style="font-size: 0.7em;"
                            onclick="removeDiscount(${discount.id})" title="Remove discount"></button>
                `;
                discountList.appendChild(discountTag);
            });
        }
    } else {
        if (appliedDiscountsSection) appliedDiscountsSection.style.display = 'none';
    }
}

function updateCartTotals() {
    const discountPercent = parseFloat(document.getElementById('id_discount_percent').value) || 0;
    const subtotal = cartData.subtotal;
    const manualDiscount = subtotal * (discountPercent / 100);
    const totalDiscount = manualDiscount + totalDiscountAmount;
    const total = subtotal - totalDiscount;

    document.getElementById('cart-subtotal').textContent = `₱${subtotal.toFixed(2)}`;
    document.getElementById('cart-discount').textContent = `₱${totalDiscount.toFixed(2)}`;
    document.getElementById('cart-total').textContent = `₱${total.toFixed(2)}`;
}

function getCurrentTotal() {
    const discountPercent = parseFloat(document.getElementById('id_discount_percent').value) || 0;
    const subtotal = cartData.subtotal;
    const manualDiscount = subtotal * (discountPercent / 100);
    const totalDiscount = manualDiscount + totalDiscountAmount;
    return subtotal - totalDiscount;
}

function showMessage(message, type) {
    // Create a simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>
{% endblock %}
