from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
import random
from datetime import datetime, timedelta

from inventory.models import Product, Service
from inventory.models_unified import UnifiedTransaction, UnifiedTransactionLineItem
from inventory.models_session import SessionConsumption, SessionAppointment


class Command(BaseCommand):
    help = 'Create comprehensive test data for all system features'

    def handle(self, *args, **options):
        self.stdout.write('Creating comprehensive test data...')
        
        # Update product stock
        self.update_product_stock()
        
        # Create realistic transactions
        self.create_test_transactions()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created comprehensive test data!')
        )

    def update_product_stock(self):
        """Update all products to have realistic stock levels"""
        self.stdout.write('Updating product stock levels...')
        
        products = Product.objects.all()
        for product in products:
            product.current_stock = random.randint(25, 100)
            product.save()
            
        self.stdout.write(f'Updated stock for {products.count()} products')

    def create_test_transactions(self):
        """Create comprehensive test transactions"""
        
        # Filipino customer data
        customers = [
            {'name': '<PERSON>', 'phone': '+639171234567', 'email': '<EMAIL>'},
            {'name': '<PERSON> <PERSON>riz <PERSON>', 'phone': '+639282345678', 'email': '<EMAIL>'},
            {'name': 'Carmen Isabella Cruz', 'phone': '+639393456789', 'email': '<EMAIL>'},
            {'name': 'Rosa Gabriela Gonzales', 'phone': '+639174567890', 'email': '<EMAIL>'},
            {'name': 'Elena Sofia Mendoza', 'phone': '+639285678901', 'email': '<EMAIL>'},
            {'name': 'Sofia Camila Dela Cruz', 'phone': '+639396789012', 'email': '<EMAIL>'},
            {'name': 'Isabella Valentina Garcia', 'phone': '+639177890123', 'email': '<EMAIL>'},
            {'name': 'Camila Andrea Rodriguez', 'phone': '+639288901234', 'email': '<EMAIL>'},
            {'name': 'Valentina Nicole Lopez', 'phone': '+639399012345', 'email': '<EMAIL>'},
            {'name': 'Gabriela Michelle Morales', 'phone': '+639170123456', 'email': '<EMAIL>'},
            {'name': 'Stephanie Mae Villanueva', 'phone': '+639281234567', 'email': '<EMAIL>'},
            {'name': 'Andrea Joy Fernandez', 'phone': '+639392345678', 'email': '<EMAIL>'},
            {'name': 'Nicole Grace Tan', 'phone': '+639173456789', 'email': '<EMAIL>'},
            {'name': 'Michelle Anne Lim', 'phone': '+639284567890', 'email': '<EMAIL>'},
            {'name': 'Jasmine Rose Flores', 'phone': '+639395678901', 'email': '<EMAIL>'},
        ]
        
        staff_members = [
            'Dr. Sarah Martinez', 'Nurse Joy Aquino', 'Therapist Anna Lim', 
            'Dr. Michael Santos', 'Aesthetician Lisa Chen', 'Dermatologist Mark Tan'
        ]
        
        products = list(Product.objects.all())
        services = list(Service.objects.filter(is_active=True))
        
        # Create 20 transactions with various scenarios
        for i in range(20):
            customer = customers[i % len(customers)]
            
            try:
                if i < 5:
                    # Product-only transactions
                    self.create_product_transaction(customer, products, staff_members, i + 1)
                elif i < 10:
                    # Service-only transactions
                    self.create_service_transaction(customer, services, staff_members, i + 1)
                else:
                    # Mixed transactions
                    self.create_mixed_transaction(customer, products, services, staff_members, i + 1)
                    
                self.stdout.write(f'✓ Created transaction {i + 1}')
            except Exception as e:
                self.stdout.write(f'✗ Error creating transaction {i + 1}: {e}')

    def create_product_transaction(self, customer, products, staff_members, num):
        """Create product-only transaction"""
        
        payment_mode = random.choice(['cash', 'gcash'])
        payment_date = timezone.now() - timedelta(days=random.randint(1, 30))
        discount = random.choice([0, 0, 0, 5, 10])  # Most no discount
        
        # Calculate subtotal first
        selected_products = random.sample(products, min(random.randint(1, 3), len(products)))
        subtotal = Decimal('0.00')
        
        for product in selected_products:
            quantity = random.randint(1, 2)
            subtotal += product.selling_price * quantity
        
        # Create transaction without discount calculation
        transaction = UnifiedTransaction(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=discount,
            staff_received_by=random.choice(staff_members),
            remarks=f'Product purchase - Transaction {num}',
            subtotal=subtotal,
        )
        
        if payment_mode == 'cash':
            transaction.cash_amount_received = subtotal + Decimal('50.00')
        else:
            transaction.gcash_reference_number = f'GC{random.randint(100000000000, 999999999999)}'
        
        # Save without triggering discount calculation issues
        transaction.save()
        
        # Create line items
        for product in selected_products:
            quantity = random.randint(1, 2)
            UnifiedTransactionLineItem.objects.create(
                transaction=transaction,
                item_type='product',
                product=product,
                quantity=quantity,
                unit_price=product.selling_price,
            )

    def create_service_transaction(self, customer, services, staff_members, num):
        """Create service-only transaction with sessions"""
        
        payment_mode = random.choice(['cash', 'gcash'])
        payment_date = timezone.now() - timedelta(days=random.randint(10, 60))
        discount = random.choice([0, 0, 5, 10, 15])
        
        service = random.choice(services)
        sessions = random.randint(5, 10)
        subtotal = service.price_per_session * sessions
        
        transaction = UnifiedTransaction(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=discount,
            staff_received_by=random.choice(staff_members),
            remarks=f'Service package - {service.name} - Transaction {num}',
            subtotal=subtotal,
        )
        
        if payment_mode == 'cash':
            transaction.cash_amount_received = subtotal + Decimal('100.00')
        else:
            transaction.gcash_reference_number = f'GC{random.randint(100000000000, 999999999999)}'
        
        transaction.save()
        
        # Create service line item
        line_item = UnifiedTransactionLineItem.objects.create(
            transaction=transaction,
            item_type='service',
            service=service,
            sessions=sessions,
            unit_price=service.price_per_session,
        )
        
        # Create session scenarios
        self.create_session_scenarios(line_item, staff_members, payment_date, num)

    def create_mixed_transaction(self, customer, products, services, staff_members, num):
        """Create mixed product + service transaction"""
        
        payment_mode = random.choice(['cash', 'gcash'])
        payment_date = timezone.now() - timedelta(days=random.randint(5, 45))
        discount = random.choice([0, 0, 5, 8, 12])
        
        # Calculate subtotal
        selected_products = random.sample(products, min(2, len(products)))
        service = random.choice(services)
        sessions = random.randint(3, 6)
        
        subtotal = Decimal('0.00')
        for product in selected_products:
            subtotal += product.selling_price
        subtotal += service.price_per_session * sessions
        
        transaction = UnifiedTransaction(
            customer_name=customer['name'],
            customer_phone=customer['phone'],
            customer_email=customer['email'],
            payment_mode=payment_mode,
            payment_date=payment_date,
            discount_percent=discount,
            staff_received_by=random.choice(staff_members),
            remarks=f'Mixed purchase - Products + {service.name} - Transaction {num}',
            subtotal=subtotal,
        )
        
        if payment_mode == 'cash':
            transaction.cash_amount_received = subtotal + Decimal('200.00')
        else:
            transaction.gcash_reference_number = f'GC{random.randint(100000000000, 999999999999)}'
        
        transaction.save()
        
        # Create product line items
        for product in selected_products:
            UnifiedTransactionLineItem.objects.create(
                transaction=transaction,
                item_type='product',
                product=product,
                quantity=1,
                unit_price=product.selling_price,
            )
        
        # Create service line item
        line_item = UnifiedTransactionLineItem.objects.create(
            transaction=transaction,
            item_type='service',
            service=service,
            sessions=sessions,
            unit_price=service.price_per_session,
        )
        
        # Create session scenarios
        self.create_session_scenarios(line_item, staff_members, payment_date, num)

    def create_session_scenarios(self, line_item, staff_members, payment_date, transaction_num):
        """Create various session consumption scenarios"""
        
        total_sessions = line_item.sessions
        
        # Different scenarios based on transaction number
        if transaction_num % 5 == 0:
            # All sessions completed
            self.create_completed_sessions(line_item, staff_members, payment_date)
        elif transaction_num % 4 == 0:
            # Partial sessions with appointments
            self.create_partial_with_appointments(line_item, staff_members, payment_date)
        elif transaction_num % 3 == 0:
            # Some sessions completed
            completed = int(total_sessions * 0.6)
            self.create_partial_sessions(line_item, staff_members, payment_date, completed)
        elif transaction_num % 2 == 0:
            # Just started
            self.create_partial_sessions(line_item, staff_members, payment_date, 1)
        # else: No sessions yet (paid but not started)

    def create_completed_sessions(self, line_item, staff_members, payment_date):
        """Create all sessions as completed"""
        for session_num in range(1, line_item.sessions + 1):
            session_date = payment_date + timedelta(days=7 * session_num)
            SessionConsumption.objects.create(
                line_item=line_item,
                session_number=session_num,
                session_date=session_date,
                staff_performed_by=random.choice(staff_members),
                duration_minutes=random.randint(60, 120),
                notes=f'Session {session_num} completed successfully. Excellent progress observed.',
                customer_feedback=random.choice([
                    'Very satisfied with the results',
                    'Excellent service quality',
                    'Noticeable improvement in skin condition',
                    'Professional and caring staff',
                    'Will definitely continue treatment'
                ]),
                status='completed'
            )

    def create_partial_sessions(self, line_item, staff_members, payment_date, completed_count):
        """Create partial sessions completed"""
        for session_num in range(1, completed_count + 1):
            session_date = payment_date + timedelta(days=7 * session_num)
            SessionConsumption.objects.create(
                line_item=line_item,
                session_number=session_num,
                session_date=session_date,
                staff_performed_by=random.choice(staff_members),
                duration_minutes=random.randint(60, 120),
                notes=f'Session {session_num} - Treatment progressing well. Customer responding positively.',
                customer_feedback=random.choice([
                    'Satisfied with progress so far',
                    'Good experience, looking forward to next session',
                    'Staff is very professional',
                    'Feeling improvement already'
                ]),
                status='completed'
            )

    def create_partial_with_appointments(self, line_item, staff_members, payment_date):
        """Create some completed sessions and future appointments"""
        completed = int(line_item.sessions * 0.4)
        
        # Complete some sessions
        for session_num in range(1, completed + 1):
            session_date = payment_date + timedelta(days=7 * session_num)
            SessionConsumption.objects.create(
                line_item=line_item,
                session_number=session_num,
                session_date=session_date,
                staff_performed_by=random.choice(staff_members),
                duration_minutes=random.randint(60, 120),
                notes=f'Session {session_num} completed as scheduled. Good progress.',
                customer_feedback='Good experience, satisfied with treatment',
                status='completed'
            )
        
        # Schedule future appointments
        for session_num in range(completed + 1, min(completed + 4, line_item.sessions + 1)):
            appointment_date = timezone.now() + timedelta(days=7 * (session_num - completed))
            SessionAppointment.objects.create(
                line_item=line_item,
                appointment_date=appointment_date,
                session_number=session_num,
                assigned_staff=random.choice(staff_members),
                status=random.choice(['scheduled', 'confirmed']),
                appointment_notes=f'Session {session_num} - Follow-up treatment'
            )
