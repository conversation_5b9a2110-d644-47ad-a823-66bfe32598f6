from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q, Sum, Count, F, Case, When, DecimalField
from django.core.paginator import Paginator
from django.utils import timezone
from django.views.decorators.http import require_POST
from datetime import datetime, timedelta
import json

from .models import Service, Product
from .models_unified import UnifiedTransaction, UnifiedTransactionLineItem
from .forms_unified import (
    UnifiedTransactionForm, CartItemForm, UnifiedTransactionSearchForm
)
from .utils.cart import ShoppingCart


@login_required
def unified_dashboard(request):
    """Unified dashboard showing analytics for both products and services"""
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # Basic counts
    total_products = Product.objects.filter(is_active=True).count()
    total_services = Service.objects.filter(is_active=True).count()
    total_transactions = UnifiedTransaction.objects.count()

    # Revenue analytics
    today_revenue = UnifiedTransaction.objects.filter(
        payment_date__date=today,
        payment_status='paid'
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    week_revenue = UnifiedTransaction.objects.filter(
        payment_date__date__gte=week_ago,
        payment_status='paid'
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    month_revenue = UnifiedTransaction.objects.filter(
        payment_date__date__gte=month_ago,
        payment_status='paid'
    ).aggregate(total=Sum('total_amount'))['total'] or 0

    # Service category analytics
    service_category_stats = Service.objects.values('category').annotate(
        count=Count('id'),
        total_revenue=Sum('unifiedtransactionlineitem__line_total')
    ).order_by('-total_revenue')

    # Product category analytics
    product_category_stats = Product.objects.values('category').annotate(
        count=Count('id'),
        total_revenue=Sum('unifiedtransactionlineitem__line_total')
    ).order_by('-total_revenue')

    # Popular services
    popular_services = Service.objects.annotate(
        transaction_count=Count('unifiedtransactionlineitem'),
        total_revenue=Sum('unifiedtransactionlineitem__line_total')
    ).filter(transaction_count__gt=0).order_by('-transaction_count')[:5]

    # Popular products
    popular_products = Product.objects.annotate(
        transaction_count=Count('unifiedtransactionlineitem'),
        total_revenue=Sum('unifiedtransactionlineitem__line_total')
    ).filter(transaction_count__gt=0).order_by('-transaction_count')[:5]

    # Recent transactions
    recent_transactions = UnifiedTransaction.objects.select_related().order_by('-payment_date')[:10]

    # Low stock products
    low_stock_products = Product.objects.filter(
        is_active=True,
        current_stock__lte=F('minimum_stock')
    ).order_by('current_stock')[:5]

    context = {
        'stats': {
            'total_products': total_products,
            'total_services': total_services,
            'total_transactions': total_transactions,
            'today_revenue': today_revenue,
            'week_revenue': week_revenue,
            'month_revenue': month_revenue,
        },
        'service_category_stats': service_category_stats,
        'product_category_stats': product_category_stats,
        'popular_services': popular_services,
        'popular_products': popular_products,
        'recent_transactions': recent_transactions,
        'low_stock_products': low_stock_products,
        'page_title': 'POS Dashboard'
    }
    return render(request, 'unified/dashboard.html', context)


@login_required
def unified_sales_report(request):
    """Comprehensive sales report for products and services"""
    from django.db.models import Q, Sum, Count, Avg
    from datetime import datetime, timedelta

    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    category_filter = request.GET.get('category', '')
    item_type_filter = request.GET.get('item_type', '')  # 'product' or 'service'

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Base queryset for transactions in date range
    transactions_qs = UnifiedTransaction.objects.filter(
        payment_date__date__gte=start_date,
        payment_date__date__lte=end_date,
        payment_status='paid'
    )

    # Line items queryset
    line_items_qs = UnifiedTransactionLineItem.objects.filter(
        transaction__payment_date__date__gte=start_date,
        transaction__payment_date__date__lte=end_date,
        transaction__payment_status='paid'
    )

    # Apply filters
    if item_type_filter:
        line_items_qs = line_items_qs.filter(item_type=item_type_filter)

    if category_filter:
        if item_type_filter == 'product':
            line_items_qs = line_items_qs.filter(product__category=category_filter)
        elif item_type_filter == 'service':
            line_items_qs = line_items_qs.filter(service__category=category_filter)

    # Summary statistics
    total_transactions = transactions_qs.count()
    total_revenue = transactions_qs.aggregate(total=Sum('total_amount'))['total'] or 0
    average_transaction = transactions_qs.aggregate(avg=Avg('total_amount'))['avg'] or 0

    # Product sales summary
    product_sales = line_items_qs.filter(item_type='product').values(
        'product__name', 'product__category', 'product__selling_price'
    ).annotate(
        total_quantity=Sum('quantity'),
        total_revenue=Sum('line_total'),
        transaction_count=Count('transaction', distinct=True)
    ).order_by('-total_revenue')

    # Service sales summary
    service_sales = line_items_qs.filter(item_type='service').values(
        'service__name', 'service__category', 'service__price_per_session'
    ).annotate(
        total_sessions=Sum('sessions'),
        total_revenue=Sum('line_total'),
        transaction_count=Count('transaction', distinct=True)
    ).order_by('-total_revenue')

    # Category breakdown
    product_category_breakdown = line_items_qs.filter(item_type='product').values(
        'product__category'
    ).annotate(
        total_revenue=Sum('line_total'),
        total_quantity=Sum('quantity'),
        item_count=Count('product', distinct=True)
    ).order_by('-total_revenue')

    service_category_breakdown = line_items_qs.filter(item_type='service').values(
        'service__category'
    ).annotate(
        total_revenue=Sum('line_total'),
        total_sessions=Sum('sessions'),
        item_count=Count('service', distinct=True)
    ).order_by('-total_revenue')

    # Daily sales trend
    daily_sales = transactions_qs.extra(
        select={'day': 'date(payment_date)'}
    ).values('day').annotate(
        daily_revenue=Sum('total_amount'),
        daily_transactions=Count('id')
    ).order_by('day')

    # Payment method breakdown
    payment_breakdown = transactions_qs.values('payment_mode').annotate(
        count=Count('id'),
        total_amount=Sum('total_amount')
    ).order_by('-total_amount')

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'category_filter': category_filter,
        'item_type_filter': item_type_filter,
        'summary': {
            'total_transactions': total_transactions,
            'total_revenue': total_revenue,
            'average_transaction': average_transaction,
        },
        'product_sales': product_sales,
        'service_sales': service_sales,
        'product_category_breakdown': product_category_breakdown,
        'service_category_breakdown': service_category_breakdown,
        'daily_sales': daily_sales,
        'payment_breakdown': payment_breakdown,
        'page_title': 'Sales Report'
    }
    return render(request, 'unified/sales_report.html', context)


@login_required
def unified_transaction_create(request):
    """Create a new unified transaction with shopping cart"""
    cart = ShoppingCart(request)
    
    if request.method == 'POST':
        form = UnifiedTransactionForm(request.POST)
        if form.is_valid() and not cart.is_empty():
            # Validate cart stock before creating transaction
            stock_errors = cart.validate_stock()
            if stock_errors:
                for error in stock_errors:
                    error_msg = error.get('error', f'Only {error.get("available", 0)} available')
                    messages.error(request, f"Stock error for {error['item_name']}: {error_msg}")
                return redirect('unified_transaction_create')
            
            # Create the transaction
            transaction = form.save(commit=False)
            transaction.subtotal = cart.get_subtotal()
            transaction.save()
            
            # Create line items from cart
            for item in cart.get_items():
                line_item = UnifiedTransactionLineItem(
                    transaction=transaction,
                    item_type=item['item_type'],
                    unit_price=item['unit_price']
                )
                
                if item['item_type'] == 'product':
                    line_item.product_id = item['product_id']
                    line_item.quantity = item['quantity']
                else:
                    line_item.service_id = item['service_id']
                    line_item.sessions = item['sessions']
                
                line_item.save()
            
            # Clear the cart
            cart.clear()
            
            messages.success(
                request, 
                f'Transaction {transaction.transaction_id} created successfully!'
            )
            return redirect('unified_transaction_detail', pk=transaction.pk)
    else:
        form = UnifiedTransactionForm()
    
    # Get products and services for AJAX
    products = Product.objects.filter(is_active=True).values(
        'id', 'name', 'selling_price', 'current_stock', 'unit'
    )
    services = Service.objects.filter(is_active=True).values(
        'id', 'name', 'price_per_session', 'duration_minutes'
    )
    
    # Convert Decimal values to float for JSON serialization
    products_list = []
    for product in products:
        product_data = dict(product)
        product_data['selling_price'] = float(product_data['selling_price'])
        products_list.append(product_data)
    
    services_list = []
    for service in services:
        service_data = dict(service)
        service_data['price_per_session'] = float(service_data['price_per_session'])
        services_list.append(service_data)
    
    context = {
        'form': form,
        'cart': cart,
        'cart_items': cart.get_items(),
        'cart_subtotal': cart.get_subtotal(),
        'products_json': json.dumps(products_list),
        'services_json': json.dumps(services_list),
        'page_title': 'Create Unified Transaction'
    }
    return render(request, 'unified/transaction_form.html', context)


@require_POST
@login_required
def cart_add_item(request):
    """Add item to cart via AJAX"""
    cart = ShoppingCart(request)
    
    try:
        data = json.loads(request.body)
        item_type = data.get('item_type')
        
        if item_type == 'product':
            product_id = data.get('product_id')
            quantity = int(data.get('quantity', 1))
            
            product = get_object_or_404(Product, id=product_id, is_active=True)
            
            if quantity > product.current_stock:
                return JsonResponse({
                    'success': False,
                    'error': f'Insufficient stock. Available: {product.current_stock}'
                })
            
            final_quantity = cart.add_product(product, quantity)
            item_name = product.name
            
        elif item_type == 'service':
            service_id = data.get('service_id')
            sessions = int(data.get('sessions', 1))
            
            service = get_object_or_404(Service, id=service_id, is_active=True)
            final_sessions = cart.add_service(service, sessions)
            item_name = service.name
            
        else:
            return JsonResponse({'success': False, 'error': 'Invalid item type'})
        
        return JsonResponse({
            'success': True,
            'message': f'{item_name} added to cart',
            'cart_data': cart.to_dict()
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@require_POST
@login_required
def cart_update_item(request):
    """Update item quantity in cart via AJAX"""
    cart = ShoppingCart(request)
    
    try:
        data = json.loads(request.body)
        item_key = data.get('item_key')
        quantity = int(data.get('quantity', 1))
        
        cart.update_item_quantity(item_key, quantity)
        
        return JsonResponse({
            'success': True,
            'cart_data': cart.to_dict()
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@require_POST
@login_required
def cart_remove_item(request):
    """Remove item from cart via AJAX"""
    cart = ShoppingCart(request)
    
    try:
        data = json.loads(request.body)
        item_key = data.get('item_key')
        
        cart.remove_item(item_key)
        
        return JsonResponse({
            'success': True,
            'cart_data': cart.to_dict()
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@require_POST
@login_required
def cart_clear(request):
    """Clear all items from cart via AJAX"""
    cart = ShoppingCart(request)
    cart.clear()
    
    return JsonResponse({
        'success': True,
        'cart_data': cart.to_dict()
    })


@login_required
def cart_data(request):
    """Get current cart data via AJAX"""
    cart = ShoppingCart(request)
    return JsonResponse(cart.to_dict())


@login_required
def unified_transaction_list(request):
    """List all unified transactions with search and filtering"""
    form = UnifiedTransactionSearchForm(request.GET)
    transactions = UnifiedTransaction.objects.prefetch_related('line_items').all()
    
    # Apply filters
    if form.is_valid():
        search = form.cleaned_data.get('search')
        if search:
            transactions = transactions.filter(
                Q(transaction_id__icontains=search) |
                Q(customer_name__icontains=search) |
                Q(customer_email__icontains=search)
            )
        
        status = form.cleaned_data.get('status')
        if status:
            transactions = transactions.filter(status=status)
        
        payment_mode = form.cleaned_data.get('payment_mode')
        if payment_mode:
            transactions = transactions.filter(payment_mode=payment_mode)
        
        date_from = form.cleaned_data.get('date_from')
        if date_from:
            transactions = transactions.filter(payment_date__date__gte=date_from)
        
        date_to = form.cleaned_data.get('date_to')
        if date_to:
            transactions = transactions.filter(payment_date__date__lte=date_to)
        
        min_amount = form.cleaned_data.get('min_amount')
        if min_amount:
            transactions = transactions.filter(total_amount__gte=min_amount)
        
        max_amount = form.cleaned_data.get('max_amount')
        if max_amount:
            transactions = transactions.filter(total_amount__lte=max_amount)
    
    # Pagination
    paginator = Paginator(transactions, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistics
    stats = {
        'total_transactions': transactions.count(),
        'active_transactions': transactions.filter(status='active').count(),
        'completed_transactions': transactions.filter(status='completed').count(),
        'total_revenue': transactions.aggregate(Sum('total_amount'))['total_amount__sum'] or 0,
    }
    
    context = {
        'page_obj': page_obj,
        'form': form,
        'stats': stats,
        'page_title': 'Unified Transactions'
    }
    return render(request, 'unified/transaction_list.html', context)


@login_required
def unified_transaction_detail(request, pk):
    """View unified transaction details"""
    transaction = get_object_or_404(UnifiedTransaction, pk=pk)
    line_items = transaction.line_items.all().order_by('id')
    
    context = {
        'transaction': transaction,
        'line_items': line_items,
        'page_title': f'Transaction: {transaction.transaction_id}'
    }
    return render(request, 'unified/transaction_detail.html', context)
