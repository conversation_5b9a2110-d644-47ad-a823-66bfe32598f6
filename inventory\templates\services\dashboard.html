{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold text-primary mb-0">
                <i class="fas fa-tachometer-alt me-2"></i>{{ page_title }}
            </h2>
            <p class="text-muted mb-0">Overview of aesthetic services and transactions</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Services
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_services }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-spa fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Transactions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.active_transactions }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Today's Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₱{{ stats.revenue_today|floatformat:2|intcomma }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-peso-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Monthly Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₱{{ stats.revenue_month|floatformat:2|intcomma }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Recent Transactions -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Transactions</h6>
                    <a href="{% url 'service_transaction_list' %}" class="btn btn-primary btn-sm">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_transactions %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Transaction ID</th>
                                    <th>Customer</th>
                                    <th>Service</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for line_item in recent_transactions %}
                                <tr>
                                    <td>{{ line_item.transaction.transaction_id }}</td>
                                    <td>{{ line_item.transaction.customer_name }}</td>
                                    <td>{{ line_item.service.name }}</td>
                                    <td>₱{{ line_item.total_price|floatformat:2 }}</td>
                                    <td>
                                        {% if line_item.sessions_used >= line_item.sessions %}
                                            <span class="badge badge-primary">Completed</span>
                                        {% elif line_item.sessions_used > 0 %}
                                            <span class="badge badge-success">Active</span>
                                        {% else %}
                                            <span class="badge badge-warning">Unused</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ line_item.transaction.payment_date|date:"M d, Y" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Recent Transactions</h5>
                        <p class="text-muted">Start by creating your first service transaction.</p>
                        <a href="{% url 'service_transaction_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Transaction
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Popular Services -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Popular Services</h6>
                    <a href="{% url 'service_list' %}" class="btn btn-primary btn-sm">Manage</a>
                </div>
                <div class="card-body">
                    {% if popular_services %}
                    {% for service in popular_services %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <div class="icon-circle bg-primary">
                                <i class="fas fa-spa text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="small text-gray-500">{{ service.get_category_display }}</div>
                            <div class="font-weight-bold">{{ service.name }}</div>
                            <div class="text-xs text-gray-500">{{ service.transaction_count }} transaction{{ service.transaction_count|pluralize }}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-xs text-gray-500">₱{{ service.price_per_session }}</div>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-spa fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No Services Available</h6>
                        <p class="text-muted small">Add services to see popularity.</p>
                        <a href="{% url 'service_create' %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Add Service
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
{% endblock %}
