{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary mb-0">
                    <i class="fas fa-edit me-2"></i>{{ page_title }}
                </h2>
                <div class="d-flex gap-2">
                    <a href="{% url 'unified_transaction_detail' transaction.pk %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Transaction
                    </a>
                </div>
            </div>
            <p class="text-muted mb-0">Edit transaction details and payment information</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <!-- Transaction Info Card -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Transaction Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Transaction ID:</strong><br>
                            <span class="text-primary">{{ transaction.transaction_id }}</span>
                        </div>
                        <div class="col-sm-6">
                            <strong>Status:</strong><br>
                            <span class="badge bg-success">{{ transaction.get_status_display }}</span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Created:</strong><br>
                            {{ transaction.created_at|date:"M d, Y g:i A" }}
                        </div>
                        <div class="col-sm-6">
                            <strong>Total Amount:</strong><br>
                            <span class="h5 text-success">₱{{ transaction.total_amount|floatformat:2 }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Edit Notice</h5>
                </div>
                <div class="card-body">
                    <p class="mb-2"><strong>Important:</strong></p>
                    <ul class="mb-0">
                        <li>Only basic transaction details can be edited</li>
                        <li>Line items cannot be modified after creation</li>
                        <li>All changes are logged for audit purposes</li>
                        <li>Only active transactions can be edited</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Edit Transaction Details</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.customer_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user me-1"></i>Customer Name
                                </label>
                                {{ form.customer_name }}
                                {% if form.customer_name.errors %}
                                    <div class="text-danger small">{{ form.customer_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.payment_mode.id_for_label }}" class="form-label">
                                    <i class="fas fa-credit-card me-1"></i>Payment Method
                                </label>
                                {{ form.payment_mode }}
                                {% if form.payment_mode.errors %}
                                    <div class="text-danger small">{{ form.payment_mode.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.discount_percent.id_for_label }}" class="form-label">
                                    <i class="fas fa-percent me-1"></i>Discount Percentage
                                </label>
                                {{ form.discount_percent }}
                                {% if form.discount_percent.errors %}
                                    <div class="text-danger small">{{ form.discount_percent.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">Current discount: {{ transaction.discount_percent }}%</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.staff_received_by.id_for_label }}" class="form-label">
                                    <i class="fas fa-user-tie me-1"></i>Staff Member
                                </label>
                                {{ form.staff_received_by }}
                                {% if form.staff_received_by.errors %}
                                    <div class="text-danger small">{{ form.staff_received_by.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Payment Details -->
                        <div class="row" id="payment-details">
                            <div class="col-md-6 mb-3" id="cash-amount-field" style="display: none;">
                                <label for="{{ form.cash_amount_received.id_for_label }}" class="form-label">
                                    <i class="fas fa-money-bill me-1"></i>Cash Amount Received
                                </label>
                                {{ form.cash_amount_received }}
                                {% if form.cash_amount_received.errors %}
                                    <div class="text-danger small">{{ form.cash_amount_received.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3" id="gcash-ref-field" style="display: none;">
                                <label for="{{ form.gcash_reference_number.id_for_label }}" class="form-label">
                                    <i class="fas fa-mobile-alt me-1"></i>GCash Reference Number
                                </label>
                                {{ form.gcash_reference_number }}
                                {% if form.gcash_reference_number.errors %}
                                    <div class="text-danger small">{{ form.gcash_reference_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.remarks.id_for_label }}" class="form-label">
                                <i class="fas fa-sticky-note me-1"></i>Remarks
                            </label>
                            {{ form.remarks }}
                            {% if form.remarks.errors %}
                                <div class="text-danger small">{{ form.remarks.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- Customer Attachments -->
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-paperclip me-2"></i>Customer Documents
                            </h6>
                            <p class="text-muted small">Update or add customer documents. Leave fields empty to keep existing files.</p>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.attachment_1.id_for_label }}" class="form-label">
                                        <i class="fas fa-file me-1"></i>Document 1
                                    </label>
                                    {{ form.attachment_1 }}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.attachment_1_description.id_for_label }}" class="form-label">
                                        Description
                                    </label>
                                    {{ form.attachment_1_description }}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.attachment_2.id_for_label }}" class="form-label">
                                        <i class="fas fa-file me-1"></i>Document 2
                                    </label>
                                    {{ form.attachment_2 }}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.attachment_2_description.id_for_label }}" class="form-label">
                                        Description
                                    </label>
                                    {{ form.attachment_2_description }}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.attachment_3.id_for_label }}" class="form-label">
                                        <i class="fas fa-file me-1"></i>Document 3
                                    </label>
                                    {{ form.attachment_3 }}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.attachment_3_description.id_for_label }}" class="form-label">
                                        Description
                                    </label>
                                    {{ form.attachment_3_description }}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'unified_transaction_detail' transaction.pk %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Transaction Summary -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Transaction Summary</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span>₱{{ transaction.subtotal|floatformat:2 }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Discount:</span>
                        <span>-₱{{ transaction.discount_amount|floatformat:2 }}</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between fw-bold">
                        <span>Total:</span>
                        <span class="text-success">₱{{ transaction.total_amount|floatformat:2 }}</span>
                    </div>
                    
                    {% if transaction.payment_mode == 'cash' and transaction.cash_amount_received %}
                    <hr>
                    <div class="d-flex justify-content-between">
                        <span>Cash Received:</span>
                        <span>₱{{ transaction.cash_amount_received|floatformat:2 }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Change:</span>
                        <span>₱{{ transaction.change_amount|floatformat:2 }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentModeField = document.getElementById('{{ form.payment_mode.id_for_label }}');
    const cashAmountField = document.getElementById('cash-amount-field');
    const gcashRefField = document.getElementById('gcash-ref-field');
    
    function togglePaymentFields() {
        const paymentMode = paymentModeField.value;
        
        if (paymentMode === 'cash') {
            cashAmountField.style.display = 'block';
            gcashRefField.style.display = 'none';
        } else if (paymentMode === 'gcash') {
            cashAmountField.style.display = 'none';
            gcashRefField.style.display = 'block';
        } else {
            cashAmountField.style.display = 'none';
            gcashRefField.style.display = 'none';
        }
    }
    
    // Initial toggle
    togglePaymentFields();
    
    // Toggle on change
    paymentModeField.addEventListener('change', togglePaymentFields);
});
</script>
{% endblock %}
