{% extends 'base.html' %}
{% load static %}

{% block title %}Session Reports{% endblock %}

{% block extra_css %}
<style>
    .report-card {
        transition: transform 0.2s;
    }
    .report-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
    }
    .chart-container {
        position: relative;
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{% url 'customer_sessions_dashboard' %}">Session Management</a>
                            </li>
                            <li class="breadcrumb-item active">Reports</li>
                        </ol>
                    </nav>
                    <h2><i class="fas fa-chart-bar text-primary"></i> Session Reports</h2>
                    <p class="text-muted">Analytics and insights for {{ start_date|date:"M d" }} - {{ end_date|date:"M d, Y" }}</p>
                </div>
                <div class="text-end">
                    <a href="{% url 'customer_sessions_dashboard' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="{% url 'session_calendar' %}" class="btn btn-outline-primary">
                        <i class="fas fa-calendar-alt"></i> Calendar
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label class="form-label">Start Date</label>
                            <input type="date" class="form-control" name="start_date" value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">End Date</label>
                            <input type="date" class="form-control" name="end_date" value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Update Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Metrics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h3>{{ total_sessions_completed }}</h3>
                    <p class="mb-0">Sessions Completed</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h3>{{ service_stats|length }}</h3>
                    <p class="mb-0">Services Provided</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h3>{{ date_range_days }}</h3>
                    <p class="mb-0">Days in Period</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <h3>{{ total_sessions_completed|floatformat:1 }}</h3>
                    <p class="mb-0">Avg Sessions/Day</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Service Performance -->
    <div class="row mb-4">
        <div class="col">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-spa me-2"></i>Service Performance</h5>
                </div>
                <div class="card-body">
                    {% if service_stats %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Service Name</th>
                                    <th>Sessions Completed</th>
                                    <th>Unique Customers</th>
                                    <th>Avg Duration (min)</th>
                                    <th>Total Duration (hrs)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for service in service_stats %}
                                <tr>
                                    <td>
                                        <strong>{{ service.service_name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ service.sessions_completed }}</span>
                                    </td>
                                    <td>{{ service.unique_customers }}</td>
                                    <td>{{ service.avg_duration|floatformat:0 }}</td>
                                    <td>{{ service.total_duration|floatformat:1 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Session Data</h5>
                        <p class="text-muted">No sessions were completed in the selected date range.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-pie-chart me-2"></i>Sessions by Service</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="serviceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bar-chart me-2"></i>Customer Distribution</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="customerChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics -->
    <div class="row">
        <div class="col-md-6">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Session Duration Analysis</h5>
                </div>
                <div class="card-body">
                    {% if service_stats %}
                    {% for service in service_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ service.service_name|truncatechars:25 }}</span>
                        <div class="progress flex-grow-1 mx-3" style="height: 20px;">
                            <div class="progress-bar" style="width: {% widthratio service.avg_duration 120 100 %}%">
                                {{ service.avg_duration|floatformat:0 }}min
                            </div>
                        </div>
                        <small class="text-muted">{{ service.sessions_completed }} sessions</small>
                    </div>
                    {% endfor %}
                    {% else %}
                    <p class="text-muted text-center">No duration data available</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card report-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top Performing Services</h5>
                </div>
                <div class="card-body">
                    {% if service_stats %}
                    {% for service in service_stats|slice:":5" %}
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-1">{{ service.service_name }}</h6>
                            <small class="text-muted">{{ service.unique_customers }} customers</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-success fs-6">{{ service.sessions_completed }}</span>
                            <br><small class="text-muted">sessions</small>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <p class="text-muted text-center">No performance data available</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Service Distribution Chart
const serviceCtx = document.getElementById('serviceChart').getContext('2d');
const serviceData = {
    labels: [
        {% for service in service_stats %}
        '{{ service.service_name|truncatechars:20 }}',
        {% endfor %}
    ],
    datasets: [{
        data: [
            {% for service in service_stats %}
            {{ service.sessions_completed }},
            {% endfor %}
        ],
        backgroundColor: [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
            '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
        ]
    }]
};

new Chart(serviceCtx, {
    type: 'doughnut',
    data: serviceData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Customer Distribution Chart
const customerCtx = document.getElementById('customerChart').getContext('2d');
const customerData = {
    labels: [
        {% for service in service_stats %}
        '{{ service.service_name|truncatechars:15 }}',
        {% endfor %}
    ],
    datasets: [{
        label: 'Unique Customers',
        data: [
            {% for service in service_stats %}
            {{ service.unique_customers }},
            {% endfor %}
        ],
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
    }]
};

new Chart(customerCtx, {
    type: 'bar',
    data: customerData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
{% endblock %}
