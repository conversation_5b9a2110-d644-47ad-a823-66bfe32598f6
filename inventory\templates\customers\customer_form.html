{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary mb-0">
                    <i class="fas fa-user-{% if customer %}edit{% else %}plus{% endif %} me-2"></i>{{ page_title }}
                </h2>
                <div class="d-flex gap-2">
                    {% if customer %}
                        <a href="{% url 'customer_detail' customer.pk %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Customer
                        </a>
                    {% else %}
                        <a href="{% url 'customer_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Customers
                        </a>
                    {% endif %}
                </div>
            </div>
            <p class="text-muted mb-0">{% if customer %}Update customer information{% else %}Add a new customer to the system{% endif %}</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>Customer Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Basic Information
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user me-1"></i>{{ form.name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small mt-1">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">
                                    <i class="fas fa-birthday-cake me-1"></i>{{ form.date_of_birth.label }}
                                </label>
                                {{ form.date_of_birth }}
                                {% if form.date_of_birth.errors %}
                                    <div class="text-danger small mt-1">{{ form.date_of_birth.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-address-book me-2"></i>Contact Information
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">
                                    <i class="fas fa-phone me-1"></i>{{ form.phone.label }}
                                </label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger small mt-1">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>{{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small mt-1">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>{{ form.address.label }}
                                </label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="text-danger small mt-1">{{ form.address.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Emergency & Medical Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-heartbeat me-2"></i>Emergency & Medical Information
                                </h6>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="{{ form.emergency_contact.id_for_label }}" class="form-label">
                                    <i class="fas fa-phone-alt me-1"></i>{{ form.emergency_contact.label }}
                                </label>
                                {{ form.emergency_contact }}
                                {% if form.emergency_contact.errors %}
                                    <div class="text-danger small mt-1">{{ form.emergency_contact.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">Include name and phone number of emergency contact</div>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="{{ form.medical_notes.id_for_label }}" class="form-label">
                                    <i class="fas fa-notes-medical me-1"></i>{{ form.medical_notes.label }}
                                </label>
                                {{ form.medical_notes }}
                                {% if form.medical_notes.errors %}
                                    <div class="text-danger small mt-1">{{ form.medical_notes.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">Include allergies, medical conditions, or special instructions</div>
                            </div>
                        </div>

                        <!-- Customer Documents -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="text-primary mb-0">
                                        <i class="fas fa-paperclip me-2"></i>Customer Documents (Optional)
                                    </h6>
                                    <button type="button" id="toggle-customer-attachments" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-plus me-1"></i>Add Documents
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Attachments Container (Initially Hidden) -->
                        <div id="customer-attachments-container" style="display: none;">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <p class="text-muted small">Upload customer documents such as ID copies, medical records, insurance cards, etc. (Images, PDF, Word documents accepted)</p>
                                </div>
                            </div>

                            <!-- Show existing customer attachments when editing -->
                            {% if customer and customer.pk %}
                                {% if customer.attachment_1 or customer.attachment_2 or customer.attachment_3 %}
                                <div class="card border-success mb-3">
                                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="fas fa-paperclip me-2"></i>Current Customer Documents
                                        </h6>
                                        <button type="button" class="btn btn-sm btn-light" id="add-more-customer-docs">
                                            <i class="fas fa-plus me-1"></i>Add More Documents
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            {% if customer.attachment_1 %}
                                            <div class="col-md-4 mb-3">
                                                <div class="card border-primary h-100">
                                                    <div class="card-body text-center">
                                                        {% if customer.attachment_1.url|slice:"-4:" == ".jpg" or customer.attachment_1.url|slice:"-4:" == ".png" or customer.attachment_1.url|slice:"-5:" == ".jpeg" or customer.attachment_1.url|slice:"-5:" == ".webp" or customer.attachment_1.url|slice:"-4:" == ".gif" %}
                                                            <div class="mb-2">
                                                                <img src="{{ customer.attachment_1.url }}" alt="{{ customer.attachment_1_description }}" class="img-thumbnail" style="max-height: 120px; max-width: 100%;">
                                                            </div>
                                                        {% else %}
                                                            <div class="mb-2">
                                                                <i class="fas fa-file-alt fa-3x text-success"></i>
                                                            </div>
                                                        {% endif %}
                                                        <h6 class="card-title text-success">{{ customer.attachment_1_description|default:"Document 1" }}</h6>
                                                        <p class="card-text small text-muted">
                                                            <i class="fas fa-calendar me-1"></i>{{ customer.updated_at|date:"M d, Y" }}<br>
                                                            <i class="fas fa-file me-1"></i>{{ customer.attachment_1.name|slice:"-20:" }}
                                                        </p>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ customer.attachment_1.url }}" target="_blank" class="btn btn-sm btn-success">
                                                                <i class="fas fa-eye me-1"></i>View
                                                            </a>
                                                            <a href="{{ customer.attachment_1.url }}" download class="btn btn-sm btn-outline-success">
                                                                <i class="fas fa-download me-1"></i>Download
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if customer.attachment_2 %}
                                            <div class="col-md-4 mb-3">
                                                <div class="card border-primary h-100">
                                                    <div class="card-body text-center">
                                                        {% if customer.attachment_2.url|slice:"-4:" == ".jpg" or customer.attachment_2.url|slice:"-4:" == ".png" or customer.attachment_2.url|slice:"-5:" == ".jpeg" or customer.attachment_2.url|slice:"-5:" == ".webp" or customer.attachment_2.url|slice:"-4:" == ".gif" %}
                                                            <div class="mb-2">
                                                                <img src="{{ customer.attachment_2.url }}" alt="{{ customer.attachment_2_description }}" class="img-thumbnail" style="max-height: 120px; max-width: 100%;">
                                                            </div>
                                                        {% else %}
                                                            <div class="mb-2">
                                                                <i class="fas fa-file-alt fa-3x text-success"></i>
                                                            </div>
                                                        {% endif %}
                                                        <h6 class="card-title text-success">{{ customer.attachment_2_description|default:"Document 2" }}</h6>
                                                        <p class="card-text small text-muted">
                                                            <i class="fas fa-calendar me-1"></i>{{ customer.updated_at|date:"M d, Y" }}<br>
                                                            <i class="fas fa-file me-1"></i>{{ customer.attachment_2.name|slice:"-20:" }}
                                                        </p>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ customer.attachment_2.url }}" target="_blank" class="btn btn-sm btn-success">
                                                                <i class="fas fa-eye me-1"></i>View
                                                            </a>
                                                            <a href="{{ customer.attachment_2.url }}" download class="btn btn-sm btn-outline-success">
                                                                <i class="fas fa-download me-1"></i>Download
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% if customer.attachment_3 %}
                                            <div class="col-md-4 mb-3">
                                                <div class="card border-primary h-100">
                                                    <div class="card-body text-center">
                                                        {% if customer.attachment_3.url|slice:"-4:" == ".jpg" or customer.attachment_3.url|slice:"-4:" == ".png" or customer.attachment_3.url|slice:"-5:" == ".jpeg" or customer.attachment_3.url|slice:"-5:" == ".webp" or customer.attachment_3.url|slice:"-4:" == ".gif" %}
                                                            <div class="mb-2">
                                                                <img src="{{ customer.attachment_3.url }}" alt="{{ customer.attachment_3_description }}" class="img-thumbnail" style="max-height: 120px; max-width: 100%;">
                                                            </div>
                                                        {% else %}
                                                            <div class="mb-2">
                                                                <i class="fas fa-file-alt fa-3x text-success"></i>
                                                            </div>
                                                        {% endif %}
                                                        <h6 class="card-title text-success">{{ customer.attachment_3_description|default:"Document 3" }}</h6>
                                                        <p class="card-text small text-muted">
                                                            <i class="fas fa-calendar me-1"></i>{{ customer.updated_at|date:"M d, Y" }}<br>
                                                            <i class="fas fa-file me-1"></i>{{ customer.attachment_3.name|slice:"-20:" }}
                                                        </p>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ customer.attachment_3.url }}" target="_blank" class="btn btn-sm btn-success">
                                                                <i class="fas fa-eye me-1"></i>View
                                                            </a>
                                                            <a href="{{ customer.attachment_3.url }}" download class="btn btn-sm btn-outline-success">
                                                                <i class="fas fa-download me-1"></i>Download
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            {% endif %}

                            <!-- Customer Attachment 1 (Always available when container is shown) -->
                            <div class="customer-attachment-row row mb-3" id="customer-attachment-1-row">
                                <div class="col-md-5">
                                    <label for="{{ form.attachment_1.id_for_label }}" class="form-label">
                                        <i class="fas fa-file me-1"></i>Document 1
                                    </label>
                                    {{ form.attachment_1 }}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.attachment_1_description.id_for_label }}" class="form-label">
                                        Description
                                    </label>
                                    {{ form.attachment_1_description }}
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-success btn-sm" id="add-more-customer-attachments" title="Add More Documents">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Customer Attachment 2 (Hidden initially) -->
                            <div class="customer-attachment-row row mb-3" id="customer-attachment-2-row" style="display: none;">
                                <div class="col-md-5">
                                    <label for="{{ form.attachment_2.id_for_label }}" class="form-label">
                                        <i class="fas fa-file me-1"></i>Document 2
                                    </label>
                                    {{ form.attachment_2 }}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.attachment_2_description.id_for_label }}" class="form-label">
                                        Description
                                    </label>
                                    {{ form.attachment_2_description }}
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-danger btn-sm remove-customer-attachment" data-target="customer-attachment-2-row" title="Remove Document">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Customer Attachment 3 (Hidden initially) -->
                            <div class="customer-attachment-row row mb-3" id="customer-attachment-3-row" style="display: none;">
                                <div class="col-md-5">
                                    <label for="{{ form.attachment_3.id_for_label }}" class="form-label">
                                        <i class="fas fa-file me-1"></i>Document 3
                                    </label>
                                    {{ form.attachment_3 }}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.attachment_3_description.id_for_label }}" class="form-label">
                                        Description
                                    </label>
                                    {{ form.attachment_3_description }}
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-danger btn-sm remove-customer-attachment" data-target="customer-attachment-3-row" title="Remove Document">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            {% if customer %}
                                <a href="{% url 'customer_detail' customer.pk %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            {% else %}
                                <a href="{% url 'customer_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            {% endif %}
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{% if customer %}Update Customer{% else %}Create Customer{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Help Card -->
        <div class="col-lg-4">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Customer Information Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Name</strong> is required for all customers
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Phone & Email</strong> help with appointment reminders
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Medical Notes</strong> are important for treatment safety
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Emergency Contact</strong> is recommended for all treatments
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            All information is kept confidential and secure
                        </li>
                    </ul>
                </div>
            </div>

            {% if customer %}
            <div class="card border-warning mt-3">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Edit Notice</h6>
                </div>
                <div class="card-body">
                    <p class="mb-2"><strong>Important:</strong></p>
                    <ul class="mb-0">
                        <li>Changes are logged for audit purposes</li>
                        <li>Transaction history remains unchanged</li>
                        <li>Name changes affect future transactions only</li>
                    </ul>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Customer attachment functionality
    const toggleCustomerAttachmentsBtn = document.getElementById('toggle-customer-attachments');
    const customerAttachmentsContainer = document.getElementById('customer-attachments-container');
    const addMoreCustomerBtn = document.getElementById('add-more-customer-attachments');
    const addMoreCustomerDocsBtn = document.getElementById('add-more-customer-docs');
    let customerAttachmentCount = 1;

    if (toggleCustomerAttachmentsBtn && customerAttachmentsContainer) {
        // Toggle customer attachments container
        toggleCustomerAttachmentsBtn.addEventListener('click', function() {
            if (customerAttachmentsContainer.style.display === 'none') {
                customerAttachmentsContainer.style.display = 'block';
                toggleCustomerAttachmentsBtn.innerHTML = '<i class="fas fa-minus me-1"></i>Hide Documents';
                toggleCustomerAttachmentsBtn.classList.remove('btn-outline-primary');
                toggleCustomerAttachmentsBtn.classList.add('btn-outline-secondary');
            } else {
                customerAttachmentsContainer.style.display = 'none';
                toggleCustomerAttachmentsBtn.innerHTML = '<i class="fas fa-plus me-1"></i>Add Documents';
                toggleCustomerAttachmentsBtn.classList.remove('btn-outline-secondary');
                toggleCustomerAttachmentsBtn.classList.add('btn-outline-primary');

                // Clear all attachment fields when hiding
                document.querySelectorAll('.customer-attachment-row input[type="file"]').forEach(input => {
                    input.value = '';
                });
                document.querySelectorAll('.customer-attachment-row input[type="text"]').forEach(input => {
                    input.value = '';
                });

                // Hide additional attachment rows
                const row2 = document.getElementById('customer-attachment-2-row');
                const row3 = document.getElementById('customer-attachment-3-row');
                if (row2) row2.style.display = 'none';
                if (row3) row3.style.display = 'none';
                customerAttachmentCount = 1;
                updateCustomerAddMoreButton();
            }
        });

        // Add more customer attachments
        if (addMoreCustomerBtn) {
            addMoreCustomerBtn.addEventListener('click', function() {
                if (customerAttachmentCount < 3) {
                    customerAttachmentCount++;
                    const nextRow = document.getElementById(`customer-attachment-${customerAttachmentCount}-row`);
                    if (nextRow) {
                        nextRow.style.display = 'flex';
                        updateCustomerAddMoreButton();
                    }
                }
            });
        }

        // Remove customer attachment functionality
        document.querySelectorAll('.remove-customer-attachment').forEach(button => {
            button.addEventListener('click', function() {
                const targetRow = document.getElementById(this.dataset.target);
                if (targetRow) {
                    targetRow.style.display = 'none';

                    // Clear the inputs in the hidden row
                    targetRow.querySelectorAll('input').forEach(input => {
                        input.value = '';
                    });

                    // Update attachment count
                    customerAttachmentCount = document.querySelectorAll('.customer-attachment-row:not([style*="none"])').length;
                    updateCustomerAddMoreButton();
                }
            });
        });

        function updateCustomerAddMoreButton() {
            if (addMoreCustomerBtn) {
                if (customerAttachmentCount >= 3) {
                    addMoreCustomerBtn.style.display = 'none';
                } else {
                    addMoreCustomerBtn.style.display = 'block';
                }
            }
        }
    }

    // Handle "Add More Documents" button for existing customer documents
    if (addMoreCustomerDocsBtn) {
        addMoreCustomerDocsBtn.addEventListener('click', function() {
            // Show the attachment container if it's hidden
            if (customerAttachmentsContainer && customerAttachmentsContainer.style.display === 'none') {
                customerAttachmentsContainer.style.display = 'block';
                toggleCustomerAttachmentsBtn.innerHTML = '<i class="fas fa-minus me-1"></i>Hide Documents';
                toggleCustomerAttachmentsBtn.classList.remove('btn-outline-primary');
                toggleCustomerAttachmentsBtn.classList.add('btn-outline-secondary');
            }

            // Scroll to the attachment section
            customerAttachmentsContainer.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // Highlight the attachment section briefly
            customerAttachmentsContainer.style.border = '2px solid #28a745';
            setTimeout(function() {
                customerAttachmentsContainer.style.border = '';
            }, 2000);
        });
    }
});
</script>
{% endblock %}
