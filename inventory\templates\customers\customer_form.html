{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary mb-0">
                    <i class="fas fa-user-{% if customer %}edit{% else %}plus{% endif %} me-2"></i>{{ page_title }}
                </h2>
                <div class="d-flex gap-2">
                    {% if customer %}
                        <a href="{% url 'customer_detail' customer.pk %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Customer
                        </a>
                    {% else %}
                        <a href="{% url 'customer_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Customers
                        </a>
                    {% endif %}
                </div>
            </div>
            <p class="text-muted mb-0">{% if customer %}Update customer information{% else %}Add a new customer to the system{% endif %}</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>Customer Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Basic Information
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user me-1"></i>{{ form.name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small mt-1">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">
                                    <i class="fas fa-birthday-cake me-1"></i>{{ form.date_of_birth.label }}
                                </label>
                                {{ form.date_of_birth }}
                                {% if form.date_of_birth.errors %}
                                    <div class="text-danger small mt-1">{{ form.date_of_birth.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-address-book me-2"></i>Contact Information
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">
                                    <i class="fas fa-phone me-1"></i>{{ form.phone.label }}
                                </label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger small mt-1">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>{{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small mt-1">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>{{ form.address.label }}
                                </label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="text-danger small mt-1">{{ form.address.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Emergency & Medical Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-heartbeat me-2"></i>Emergency & Medical Information
                                </h6>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="{{ form.emergency_contact.id_for_label }}" class="form-label">
                                    <i class="fas fa-phone-alt me-1"></i>{{ form.emergency_contact.label }}
                                </label>
                                {{ form.emergency_contact }}
                                {% if form.emergency_contact.errors %}
                                    <div class="text-danger small mt-1">{{ form.emergency_contact.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">Include name and phone number of emergency contact</div>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="{{ form.medical_notes.id_for_label }}" class="form-label">
                                    <i class="fas fa-notes-medical me-1"></i>{{ form.medical_notes.label }}
                                </label>
                                {{ form.medical_notes }}
                                {% if form.medical_notes.errors %}
                                    <div class="text-danger small mt-1">{{ form.medical_notes.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">Include allergies, medical conditions, or special instructions</div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            {% if customer %}
                                <a href="{% url 'customer_detail' customer.pk %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            {% else %}
                                <a href="{% url 'customer_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            {% endif %}
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{% if customer %}Update Customer{% else %}Create Customer{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Help Card -->
        <div class="col-lg-4">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Customer Information Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Name</strong> is required for all customers
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Phone & Email</strong> help with appointment reminders
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Medical Notes</strong> are important for treatment safety
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Emergency Contact</strong> is recommended for all treatments
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            All information is kept confidential and secure
                        </li>
                    </ul>
                </div>
            </div>

            {% if customer %}
            <div class="card border-warning mt-3">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Edit Notice</h6>
                </div>
                <div class="card-body">
                    <p class="mb-2"><strong>Important:</strong></p>
                    <ul class="mb-0">
                        <li>Changes are logged for audit purposes</li>
                        <li>Transaction history remains unchanged</li>
                        <li>Name changes affect future transactions only</li>
                    </ul>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
