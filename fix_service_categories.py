#!/usr/bin/env python3
"""
Script to fix service categorization based on Excel sheet sources
Run with: python manage.py shell < fix_service_categories.py
"""

from inventory.models import Service

def fix_service_categories():
    """Fix service categories based on their sheet source"""
    
    # Define category mapping based on sheet names
    sheet_to_category = {
        'FACIAL': 'facial_treatment',
        'GLUTA': 'gluta_treatment', 
        'MESO': 'aesthetic_procedure',
        'OPT': 'ipl_treatment',
        'RF': 'aesthetic_procedure',
        'HIFUWARTS': 'aesthetic_procedure',
        'CARBON LASER': 'aesthetic_procedure',
        'INVASIVE': 'aesthetic_procedure',
        'PACKAGES 5+1 & 10+3': 'other'
    }
    
    print("=== FIXING SERVICE CATEGORIES ===")
    
    # Get all services
    services = Service.objects.all()
    updated_count = 0
    
    for service in services:
        # Get the sheet source from the service description or name
        sheet_source = None
        
        # Try to determine sheet source from existing data
        # This is a bit tricky since we need to reverse-engineer from the imported data
        
        # Check if service name contains keywords that indicate category
        name_lower = service.name.lower()
        
        if any(word in name_lower for word in ['facial', 'cleansing', 'hydrating', 'acne control']):
            sheet_source = 'FACIAL'
        elif any(word in name_lower for word in ['gluta', 'glutathione', 'whitening']):
            sheet_source = 'GLUTA'
        elif any(word in name_lower for word in ['meso', 'mesotherapy']):
            sheet_source = 'MESO'
        elif any(word in name_lower for word in ['ipl', 'opt', 'laser hair']):
            sheet_source = 'OPT'
        elif any(word in name_lower for word in ['rf', 'radio frequency']):
            sheet_source = 'RF'
        elif any(word in name_lower for word in ['hifu', 'wart']):
            sheet_source = 'HIFUWARTS'
        elif any(word in name_lower for word in ['carbon laser']):
            sheet_source = 'CARBON LASER'
        elif any(word in name_lower for word in ['botox', 'filler', 'thread', 'injection']):
            sheet_source = 'INVASIVE'
        elif any(word in name_lower for word in ['package', 'promo']):
            sheet_source = 'PACKAGES 5+1 & 10+3'
        
        # Update category if we found a matching sheet source
        if sheet_source and sheet_source in sheet_to_category:
            new_category = sheet_to_category[sheet_source]
            if service.category != new_category:
                old_category = service.category
                service.category = new_category
                service.save()
                updated_count += 1
                print(f"Updated '{service.name}': {old_category} → {new_category}")
    
    print(f"\n=== SUMMARY ===")
    print(f"Total services updated: {updated_count}")
    
    # Show final category distribution
    print("\n=== FINAL CATEGORY DISTRIBUTION ===")
    categories = {}
    for service in Service.objects.all():
        cat = service.category
        if cat not in categories:
            categories[cat] = 0
        categories[cat] += 1
    
    for cat, count in categories.items():
        print(f"{cat}: {count} services")

if __name__ == "__main__":
    fix_service_categories()
