# Generated by Django 5.1.6 on 2025-07-06 15:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0005_product_cash_discount_price_product_is_on_promotion_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SessionAppointment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('appointment_date', models.DateTimeField(help_text='Scheduled appointment date and time')),
                ('session_number', models.IntegerField(help_text='Which session number this appointment is for')),
                ('assigned_staff', models.CharField(blank=True, help_text='Staff member assigned to perform the service', max_length=255)),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('confirmed', 'Confirmed'), ('completed', 'Completed'), ('no_show', 'No Show'), ('cancelled', 'Cancelled'), ('rescheduled', 'Rescheduled')], default='scheduled', max_length=12)),
                ('appointment_notes', models.TextField(blank=True, help_text='Appointment notes or special instructions')),
                ('cancellation_reason', models.TextField(blank=True, help_text='Reason for cancellation if applicable')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('line_item', models.ForeignKey(help_text='The service line item this appointment is for', on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='inventory.unifiedtransactionlineitem')),
            ],
            options={
                'verbose_name': 'Session Appointment',
                'verbose_name_plural': 'Session Appointments',
                'ordering': ['appointment_date'],
                'unique_together': {('line_item', 'session_number')},
            },
        ),
        migrations.CreateModel(
            name='SessionConsumption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_number', models.IntegerField(help_text='Session number (1, 2, 3, etc.)')),
                ('session_date', models.DateTimeField(help_text='When the session was performed')),
                ('staff_performed_by', models.CharField(blank=True, help_text='Staff member who performed the service', max_length=255)),
                ('duration_minutes', models.IntegerField(blank=True, help_text='Actual duration of the session', null=True)),
                ('notes', models.TextField(blank=True, help_text='Session notes, observations, or comments')),
                ('customer_feedback', models.TextField(blank=True, help_text='Customer feedback or comments')),
                ('status', models.CharField(choices=[('completed', 'Completed'), ('no_show', 'No Show'), ('cancelled', 'Cancelled'), ('rescheduled', 'Rescheduled')], default='completed', max_length=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('line_item', models.ForeignKey(help_text='The service line item this session belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='session_consumptions', to='inventory.unifiedtransactionlineitem')),
            ],
            options={
                'verbose_name': 'Session Consumption',
                'verbose_name_plural': 'Session Consumptions',
                'ordering': ['session_number'],
                'unique_together': {('line_item', 'session_number')},
            },
        ),
    ]
