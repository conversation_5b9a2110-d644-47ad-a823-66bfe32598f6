<!DOCTYPE html>
<html>
<head>
    <title>Debug Services</title>
</head>
<body>
    <h1>Service Debug Test</h1>
    
    <div>
        <h3>Service Categories:</h3>
        <button onclick="filterServices('facial_treatment')">Facial (facial_treatment)</button>
        <button onclick="filterServices('gluta_treatment')">Gluta (gluta_treatment)</button>
        <button onclick="filterServices('ipl_treatment')">IPL (ipl_treatment)</button>
        <button onclick="filterServices('aesthetic_procedure')">Aesthetic (aesthetic_procedure)</button>
        <button onclick="filterServices('other')">Other (other)</button>
        <button onclick="filterServices('')">All</button>
    </div>
    
    <div>
        <h3>Results:</h3>
        <p>Count: <span id="count">0</span></p>
        <select id="services" size="10" style="width: 100%;">
        </select>
    </div>

    <script>
        // Sample data structure
        const servicesData = [
            {id: 1, name: "Deep Cleansing facial", category: "facial_treatment", price_per_session: 499},
            {id: 2, name: "Classic skin white IV Push", category: "gluta_treatment", price_per_session: 1500},
            {id: 3, name: "Acne control laser", category: "ipl_treatment", price_per_session: 999},
            {id: 4, name: "Botox forehead", category: "aesthetic_procedure", price_per_session: 5000},
            {id: 5, name: "Some other service", category: "other", price_per_session: 800}
        ];
        
        console.log('Test data loaded:', servicesData.length);
        
        function filterServices(categoryFilter) {
            console.log('Filtering by category:', categoryFilter);
            
            const serviceSelect = document.getElementById('services');
            const countSpan = document.getElementById('count');
            
            serviceSelect.innerHTML = '';
            
            let filteredServices = servicesData.filter(service => {
                const matchesCategory = !categoryFilter || service.category === categoryFilter;
                return matchesCategory;
            });
            
            console.log('Filtered results:', filteredServices.length);
            
            filteredServices.forEach(service => {
                const option = document.createElement('option');
                option.value = service.id;
                option.textContent = `${service.name} - P${service.price_per_session}`;
                serviceSelect.appendChild(option);
            });
            
            countSpan.textContent = filteredServices.length;
        }
        
        // Initialize
        filterServices('');
    </script>
</body>
</html>
