from django import forms
from django.core.exceptions import ValidationError
from .models import Product, Service
from .models_unified import UnifiedTransaction, UnifiedTransactionLineItem


class UnifiedTransactionForm(forms.ModelForm):
    """Form for creating unified transactions with shopping cart functionality"""

    # Additional fields for voucher handling
    voucher_codes = forms.CharField(required=False, widget=forms.HiddenInput())
    voucher_discount = forms.DecimalField(required=False, widget=forms.HiddenInput(), initial=0)

    class Meta:
        model = UnifiedTransaction
        fields = [
            'customer_name', 'customer_phone', 'customer_email',
            'discount_percent', 'payment_mode', 'payment_date', 'remarks', 'staff_received_by',
            # Payment verification fields
            'cash_amount_received', 'gcash_reference_number',
            # Voucher fields
            'voucher_codes_used', 'voucher_discount',
            # Attachment fields
            'attachment_1', 'attachment_1_description',
            'attachment_2', 'attachment_2_description',
            'attachment_3', 'attachment_3_description'
        ]
        widgets = {
            'customer_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Customer full name'
            }),
            'customer_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+63 XXX XXX XXXX'
            }),
            'customer_email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'discount_percent': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'max': '10',
                'step': '0.01',
                'value': '0'
            }),
            'payment_mode': forms.Select(attrs={'class': 'form-select'}),
            'payment_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'remarks': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Additional notes...'
            }),
            'staff_received_by': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Staff member name'
            }),
            # Payment verification widgets
            'cash_amount_received': forms.NumberInput(attrs={
                'class': 'form-control payment-field cash-field',
                'step': '0.01',
                'min': '0',
                'placeholder': 'Amount received from customer'
            }),
            'gcash_reference_number': forms.TextInput(attrs={
                'class': 'form-control payment-field gcash-field',
                'placeholder': 'GCash reference number (13 digits)',
                'maxlength': '15'
            }),
            # Attachment widgets
            'attachment_1': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*,.pdf,.doc,.docx'
            }),
            'attachment_1_description': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Description (e.g., Prescription, ID copy, etc.)'
            }),
            'attachment_2': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*,.pdf,.doc,.docx'
            }),
            'attachment_2_description': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Description (e.g., Medical certificate, etc.)'
            }),
            'attachment_3': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*,.pdf,.doc,.docx'
            }),
            'attachment_3_description': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Description (e.g., Insurance form, etc.)'
            })
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set default payment date to now
        if not self.instance.pk:
            from django.utils import timezone
            self.fields['payment_date'].initial = timezone.now()
    
    def clean_discount_percent(self):
        discount_percent = self.cleaned_data.get('discount_percent')
        if discount_percent and (discount_percent < 0 or discount_percent > 10):
            raise ValidationError("Discount percentage must be between 0% and 10%")
        return discount_percent


class CartItemForm(forms.Form):
    """Form for adding items to the shopping cart"""
    ITEM_TYPE_CHOICES = [
        ('product', 'Product'),
        ('service', 'Service')
    ]
    
    item_type = forms.ChoiceField(
        choices=ITEM_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    # Product fields
    product = forms.ModelChoiceField(
        queryset=Product.objects.filter(is_active=True),
        required=False,
        empty_label="Select a product",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    quantity = forms.IntegerField(
        required=False,
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': '1',
            'placeholder': 'Quantity'
        })
    )
    
    # Service fields
    service = forms.ModelChoiceField(
        queryset=Service.objects.filter(is_active=True),
        required=False,
        empty_label="Select a service",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    sessions = forms.IntegerField(
        required=False,
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': '1',
            'placeholder': 'Number of sessions'
        })
    )
    
    def clean(self):
        cleaned_data = super().clean()
        item_type = cleaned_data.get('item_type')
        
        if item_type == 'product':
            product = cleaned_data.get('product')
            quantity = cleaned_data.get('quantity')
            
            if not product:
                raise ValidationError("Product is required for product items")
            if not quantity or quantity <= 0:
                raise ValidationError("Quantity must be greater than 0")
            
            # Check stock availability
            if product and quantity > product.current_stock:
                raise ValidationError(f"Insufficient stock. Available: {product.current_stock} {product.unit}")
                
        elif item_type == 'service':
            service = cleaned_data.get('service')
            sessions = cleaned_data.get('sessions')
            
            if not service:
                raise ValidationError("Service is required for service items")
            if not sessions or sessions <= 0:
                raise ValidationError("Sessions must be greater than 0")
        
        return cleaned_data


class UnifiedTransactionLineItemForm(forms.ModelForm):
    """Form for editing line items in unified transactions"""
    
    class Meta:
        model = UnifiedTransactionLineItem
        fields = ['quantity', 'sessions', 'unit_price']
        widgets = {
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'sessions': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'unit_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            })
        }
    
    def clean(self):
        cleaned_data = super().clean()
        
        if self.instance.item_type == 'product':
            quantity = cleaned_data.get('quantity')
            if not quantity or quantity <= 0:
                raise ValidationError("Quantity must be greater than 0 for products")
                
            # Check stock availability (accounting for current quantity if editing)
            if self.instance.product:
                available_stock = self.instance.product.current_stock
                if self.instance.pk:  # If editing, add back the current quantity
                    available_stock += self.instance.quantity
                
                if quantity > available_stock:
                    raise ValidationError(f"Insufficient stock. Available: {available_stock}")
                    
        elif self.instance.item_type == 'service':
            sessions = cleaned_data.get('sessions')
            if not sessions or sessions <= 0:
                raise ValidationError("Sessions must be greater than 0 for services")
        
        return cleaned_data


class UnifiedTransactionSearchForm(forms.Form):
    """Form for searching unified transactions"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by transaction ID, customer name...'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', 'All Statuses')] + UnifiedTransaction.STATUS_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    payment_mode = forms.ChoiceField(
        required=False,
        choices=[('', 'All Payment Methods')] + UnifiedTransaction.PAYMENT_MODE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    min_amount = forms.DecimalField(
        required=False,
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01',
            'placeholder': 'Min amount'
        })
    )
    
    max_amount = forms.DecimalField(
        required=False,
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01',
            'placeholder': 'Max amount'
        })
    )
