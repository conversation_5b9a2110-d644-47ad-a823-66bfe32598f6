# Generated by Django 5.1.6 on 2025-07-06 16:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0008_customer_attachment_1_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='sessionlog',
            name='attachment_1',
            field=models.FileField(blank=True, help_text='Session document (before/after photo, progress note, etc.)', null=True, upload_to='session_files/'),
        ),
        migrations.AddField(
            model_name='sessionlog',
            name='attachment_1_description',
            field=models.CharField(blank=True, help_text='Description of attachment 1', max_length=255),
        ),
        migrations.AddField(
            model_name='sessionlog',
            name='attachment_2',
            field=models.FileField(blank=True, help_text='Additional session document', null=True, upload_to='session_files/'),
        ),
        migrations.AddField(
            model_name='sessionlog',
            name='attachment_2_description',
            field=models.CharField(blank=True, help_text='Description of attachment 2', max_length=255),
        ),
        migrations.AddField(
            model_name='sessionlog',
            name='attachment_3',
            field=models.FileField(blank=True, help_text='Additional session document', null=True, upload_to='session_files/'),
        ),
        migrations.AddField(
            model_name='sessionlog',
            name='attachment_3_description',
            field=models.CharField(blank=True, help_text='Description of attachment 3', max_length=255),
        ),
    ]
