{% extends 'base.html' %}
{% load static %}

{% block title %}Session Calendar{% endblock %}

{% block extra_css %}
<style>
    .calendar-day {
        min-height: 120px;
        border: 1px solid #dee2e6;
        padding: 8px;
        background: white;
    }
    .calendar-header {
        background: #f8f9fa;
        font-weight: bold;
        text-align: center;
        padding: 10px;
        border: 1px solid #dee2e6;
    }
    .appointment-item {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 4px 8px;
        margin: 2px 0;
        border-radius: 3px;
        font-size: 0.85em;
        cursor: pointer;
    }
    .appointment-item:hover {
        background: #bbdefb;
    }
    .appointment-confirmed {
        background: #e8f5e8;
        border-left-color: #4caf50;
    }
    .appointment-overdue {
        background: #ffebee;
        border-left-color: #f44336;
    }
    .day-number {
        font-weight: bold;
        color: #666;
    }
    .today {
        background: #fff3e0 !important;
    }
    .other-month {
        color: #ccc;
        background: #f9f9f9;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{% url 'customer_sessions_dashboard' %}">Session Management</a>
                            </li>
                            <li class="breadcrumb-item active">Calendar</li>
                        </ol>
                    </nav>
                    <h2><i class="fas fa-calendar-alt text-primary"></i> Session Calendar</h2>
                    <p class="text-muted">{{ current_month }} - Scheduled Appointments</p>
                </div>
                <div class="text-end">
                    <a href="{% url 'customer_sessions_dashboard' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="{% url 'session_reports' %}" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar"></i> Reports
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar Legend -->
    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-body py-2">
                    <div class="d-flex align-items-center gap-4">
                        <span class="fw-bold">Legend:</span>
                        <div class="d-flex align-items-center">
                            <div class="appointment-item me-2" style="margin: 0;">Scheduled</div>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="appointment-item appointment-confirmed me-2" style="margin: 0;">Confirmed</div>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="appointment-item appointment-overdue me-2" style="margin: 0;">Overdue</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-body p-0">
                    <!-- Calendar Header -->
                    <div class="row g-0">
                        <div class="col calendar-header">Sunday</div>
                        <div class="col calendar-header">Monday</div>
                        <div class="col calendar-header">Tuesday</div>
                        <div class="col calendar-header">Wednesday</div>
                        <div class="col calendar-header">Thursday</div>
                        <div class="col calendar-header">Friday</div>
                        <div class="col calendar-header">Saturday</div>
                    </div>

                    <!-- Calendar Body -->
                    {% for week in calendar_weeks %}
                    <div class="row g-0">
                        {% for day in week %}
                        <div class="col calendar-day {% if day.is_today %}today{% endif %} {% if day.is_other_month %}other-month{% endif %}">
                            <div class="day-number">{{ day.day }}</div>
                            {% for appointment in day.appointments %}
                            <div class="appointment-item {% if appointment.status == 'confirmed' %}appointment-confirmed{% endif %} {% if appointment.is_overdue %}appointment-overdue{% endif %}"
                                 onclick="showAppointmentDetails({{ appointment.id }})"
                                 title="{{ appointment.service_name }} - {{ appointment.customer_name }}">
                                <div class="fw-bold">{{ appointment.appointment_date|date:"H:i" }}</div>
                                <div>{{ appointment.customer_name|truncatechars:15 }}</div>
                                <div class="text-muted small">{{ appointment.service_name|truncatechars:20 }}</div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endfor %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4>{{ total_appointments }}</h4>
                    <p class="mb-0">Total Appointments</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4>{{ confirmed_appointments }}</h4>
                    <p class="mb-0">Confirmed</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4>{{ scheduled_appointments }}</h4>
                    <p class="mb-0">Scheduled</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h4>{{ overdue_appointments }}</h4>
                    <p class="mb-0">Overdue</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Appointments List -->
    <div class="row mt-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>All Appointments This Month</h5>
                </div>
                <div class="card-body">
                    {% if appointments_by_date %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Customer</th>
                                    <th>Service</th>
                                    <th>Session #</th>
                                    <th>Staff</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for date, appointments in appointments_by_date.items %}
                                {% for appointment in appointments %}
                                <tr>
                                    <td>{{ appointment.appointment_date|date:"M d, Y H:i" }}</td>
                                    <td>{{ appointment.customer_name }}</td>
                                    <td>{{ appointment.service_name }}</td>
                                    <td>Session {{ appointment.session_number }}</td>
                                    <td>{{ appointment.assigned_staff|default:"Not assigned" }}</td>
                                    <td>
                                        <span class="badge bg-{% if appointment.status == 'confirmed' %}success{% elif appointment.status == 'scheduled' %}primary{% else %}secondary{% endif %}">
                                            {{ appointment.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{% url 'customer_session_detail' appointment.line_item.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Appointments This Month</h5>
                        <p class="text-muted">No appointments are scheduled for {{ current_month }}.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Appointment Details Modal -->
<div class="modal fade" id="appointmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Appointment Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="appointmentDetails">
                <!-- Appointment details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showAppointmentDetails(appointmentId) {
    // This would typically fetch appointment details via AJAX
    // For now, just show a placeholder
    document.getElementById('appointmentDetails').innerHTML = `
        <p>Loading appointment details for ID: ${appointmentId}</p>
        <p>This feature can be enhanced to show full appointment details.</p>
    `;
    
    new bootstrap.Modal(document.getElementById('appointmentModal')).show();
}
</script>
{% endblock %}
