{% extends 'base.html' %}
{% load static %}

{% block title %}{{ transaction.customer_name }} - Session Details{% endblock %}

{% block extra_css %}
<style>
    .session-card {
        transition: transform 0.2s;
    }
    .session-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .session-log {
        border-left: 4px solid #28a745;
        padding-left: 15px;
        margin-bottom: 15px;
    }
    .appointment-item {
        border-left: 4px solid #007bff;
        padding-left: 15px;
        margin-bottom: 15px;
    }
    .progress-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{% url 'customer_sessions_dashboard' %}">Session Management</a>
                            </li>
                            <li class="breadcrumb-item active">{{ transaction.customer_name }}</li>
                        </ol>
                    </nav>
                    <h2><i class="fas fa-user-clock text-primary"></i> {{ transaction.customer_name }}</h2>
                    <p class="text-muted">{{ service.name }} - Session Details</p>
                </div>
                <div class="text-end">
                    <a href="{% url 'customer_sessions_dashboard' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    {% if sessions_remaining > 0 %}
                    <button class="btn btn-success" onclick="showConsumeSessionModal()">
                        <i class="fas fa-check"></i> Complete Session
                    </button>
                    <button class="btn btn-primary" onclick="showScheduleModal()">
                        <i class="fas fa-calendar-plus"></i> Schedule Appointment
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <div class="progress-circle bg-primary">
                        {{ line_item.sessions }}
                    </div>
                    <h6 class="mt-2 mb-0">Total Sessions</h6>
                    <small>Purchased</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <div class="progress-circle bg-success">
                        {{ line_item.sessions_used }}
                    </div>
                    <h6 class="mt-2 mb-0">Sessions Used</h6>
                    <small>Completed</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <div class="progress-circle bg-warning">
                        {{ sessions_remaining }}
                    </div>
                    <h6 class="mt-2 mb-0">Sessions Remaining</h6>
                    <small>Available</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <div class="progress-circle bg-info">
                        ₱{{ line_item.unit_price }}
                    </div>
                    <h6 class="mt-2 mb-0">Price per Session</h6>
                    <small>Rate</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction Details -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Transaction Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>Transaction ID:</strong><br>
                            <span class="text-muted">{{ transaction.transaction_id }}</span>
                        </div>
                        <div class="col-6">
                            <strong>Purchase Date:</strong><br>
                            <span class="text-muted">{{ transaction.payment_date|date:"M d, Y H:i" }}</span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <strong>Customer Phone:</strong><br>
                            <span class="text-muted">{{ transaction.customer_phone|default:"Not provided" }}</span>
                        </div>
                        <div class="col-6">
                            <strong>Payment Method:</strong><br>
                            <span class="text-muted">{{ transaction.get_payment_mode_display }}</span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <strong>Total Paid:</strong><br>
                            <span class="text-success fw-bold">₱{{ line_item.line_total }}</span>
                        </div>
                        <div class="col-6">
                            <strong>Service:</strong><br>
                            <span class="text-muted">{{ service.name }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Session Progress</h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3" style="height: 20px;">
                        {% widthratio line_item.sessions_used line_item.sessions 100 as progress_percent %}
                        <div class="progress-bar bg-success" style="width: {{ progress_percent }}%">
                            {{ progress_percent }}%
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted">Progress: {{ line_item.sessions_used }}/{{ line_item.sessions }} sessions</span>
                        {% if sessions_remaining > 0 %}
                            <span class="badge bg-success">{{ sessions_remaining }} remaining</span>
                        {% else %}
                            <span class="badge bg-secondary">Completed</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Session History and Appointments -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>Session History</h5>
                </div>
                <div class="card-body">
                    {% for consumption in session_consumptions %}
                    <div class="session-log">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">Session {{ consumption.session_number }}</h6>
                                <small class="text-muted">
                                    {{ consumption.session_date|date:"M d, Y H:i" }}
                                    {% if consumption.staff_performed_by %}
                                        | Staff: {{ consumption.staff_performed_by }}
                                    {% endif %}
                                    {% if consumption.duration_minutes %}
                                        | Duration: {{ consumption.duration_minutes }} min
                                    {% endif %}
                                </small>
                                {% if consumption.notes %}
                                <p class="mt-2 mb-1 small">
                                    <strong>Notes:</strong> {{ consumption.notes }}
                                </p>
                                {% endif %}
                                {% if consumption.customer_feedback %}
                                <p class="mb-0 small text-info">
                                    <strong>Feedback:</strong> {{ consumption.customer_feedback }}
                                </p>
                                {% endif %}
                            </div>
                            <span class="badge bg-success">{{ consumption.get_status_display }}</span>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No sessions completed yet</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Upcoming Appointments</h5>
                </div>
                <div class="card-body">
                    {% for appointment in upcoming_appointments %}
                    <div class="appointment-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">Session {{ appointment.session_number }}</h6>
                                <small class="text-muted">
                                    {{ appointment.appointment_date|date:"M d, Y H:i" }}
                                    {% if appointment.assigned_staff %}
                                        | Staff: {{ appointment.assigned_staff }}
                                    {% endif %}
                                </small>
                                {% if appointment.appointment_notes %}
                                <p class="mt-2 mb-0 small">
                                    <strong>Notes:</strong> {{ appointment.appointment_notes }}
                                </p>
                                {% endif %}
                            </div>
                            <div class="text-end">
                                <span class="badge bg-primary">{{ appointment.get_status_display }}</span>
                                {% if appointment.status == 'scheduled' or appointment.status == 'confirmed' %}
                                <br><small class="text-muted">
                                    <button class="btn btn-sm btn-success mt-1" 
                                            onclick="completeAppointment({{ appointment.id }})">
                                        Complete
                                    </button>
                                </small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-plus fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No upcoming appointments</p>
                        {% if sessions_remaining > 0 %}
                        <button class="btn btn-primary btn-sm" onclick="showScheduleModal()">
                            <i class="fas fa-plus"></i> Schedule Next Session
                        </button>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Session Consumption Modal -->
<div class="modal fade" id="consumeSessionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Complete Session {{ next_session_number }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="consumeSessionForm">
                    <div class="mb-3">
                        <label class="form-label">Staff Performed By</label>
                        <input type="text" class="form-control" id="staffPerformedBy" 
                               placeholder="Enter staff member name">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Duration (minutes)</label>
                        <input type="number" class="form-control" id="durationMinutes" 
                               placeholder="Session duration">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Session Notes</label>
                        <textarea class="form-control" id="sessionNotes" rows="3" 
                                  placeholder="Session notes or observations..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Customer Feedback</label>
                        <textarea class="form-control" id="customerFeedback" rows="2" 
                                  placeholder="Customer feedback or comments..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="submitConsumeSession()">
                    <i class="fas fa-check"></i> Complete Session
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Appointment Modal -->
<div class="modal fade" id="scheduleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Schedule Session {{ next_session_number }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="scheduleForm">
                    <div class="mb-3">
                        <label class="form-label">Appointment Date & Time</label>
                        <input type="datetime-local" class="form-control" id="appointmentDate" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Assigned Staff</label>
                        <input type="text" class="form-control" id="assignedStaff" 
                               placeholder="Staff member to perform the service">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Appointment Notes</label>
                        <textarea class="form-control" id="appointmentNotes" rows="3" 
                                  placeholder="Special instructions or notes..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitScheduleAppointment()">
                    <i class="fas fa-calendar-plus"></i> Schedule Appointment
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showConsumeSessionModal() {
    // Clear form
    document.getElementById('staffPerformedBy').value = '';
    document.getElementById('durationMinutes').value = '';
    document.getElementById('sessionNotes').value = '';
    document.getElementById('customerFeedback').value = '';
    
    // Show modal
    new bootstrap.Modal(document.getElementById('consumeSessionModal')).show();
}

function submitConsumeSession() {
    const formData = {
        staff_performed_by: document.getElementById('staffPerformedBy').value,
        duration_minutes: document.getElementById('durationMinutes').value || null,
        notes: document.getElementById('sessionNotes').value,
        customer_feedback: document.getElementById('customerFeedback').value
    };
    
    fetch(`/sessions/consume/{{ line_item.id }}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('consumeSessionModal')).hide();
            
            // Show success message
            alert(data.message);
            
            // Reload page to update data
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while completing the session.');
    });
}

function showScheduleModal() {
    // Clear form
    document.getElementById('appointmentDate').value = '';
    document.getElementById('assignedStaff').value = '';
    document.getElementById('appointmentNotes').value = '';
    
    // Show modal
    new bootstrap.Modal(document.getElementById('scheduleModal')).show();
}

function submitScheduleAppointment() {
    const formData = {
        appointment_date: document.getElementById('appointmentDate').value,
        assigned_staff: document.getElementById('assignedStaff').value,
        appointment_notes: document.getElementById('appointmentNotes').value
    };
    
    if (!formData.appointment_date) {
        alert('Please select an appointment date and time.');
        return;
    }
    
    fetch(`/sessions/schedule/{{ line_item.id }}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('scheduleModal')).hide();
            
            // Show success message
            alert(data.message);
            
            // Reload page to update data
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while scheduling the appointment.');
    });
}
</script>
{% endblock %}
