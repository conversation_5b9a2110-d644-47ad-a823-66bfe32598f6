{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary mb-0">
                    <i class="fas fa-users me-2"></i>{{ page_title }}
                </h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                    <i class="fas fa-plus me-2"></i>Add New Customer
                </button>
            </div>
            <p class="text-muted mb-0">Manage customer information for REMR Aesthetic Centre</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Customers</h6>
                            <h3 class="mb-0">{{ customers.count }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer List -->
    <div class="row">
        {% for customer in customers %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100 shadow-sm border-0 rounded-3">
                <div class="card-body">
                    <h5 class="card-title text-primary">{{ customer.name }}</h5>
                    {% if customer.phone %}
                    <p class="card-text mb-1">
                        <i class="fas fa-phone me-2 text-muted"></i>{{ customer.phone }}
                    </p>
                    {% endif %}
                    {% if customer.email %}
                    <p class="card-text mb-1">
                        <i class="fas fa-envelope me-2 text-muted"></i>{{ customer.email }}
                    </p>
                    {% endif %}
                    <p class="card-text">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            Joined: {{ customer.created_at|date:"M d, Y" }}
                        </small>
                    </p>
                </div>
                <div class="card-footer bg-light border-0">
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm flex-fill">
                            <i class="fas fa-edit me-1"></i>Edit
                        </button>
                        <button class="btn btn-outline-info btn-sm flex-fill">
                            <i class="fas fa-eye me-1"></i>View History
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="card shadow-sm border-0 rounded-3">
                <div class="card-body text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Customers Found</h5>
                    <p class="text-muted">Start by adding your first customer.</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                        <i class="fas fa-plus me-2"></i>Add First Customer
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Redirect to new customer management -->
<script>
// Redirect Add Customer button to new customer management
document.addEventListener('DOMContentLoaded', function() {
    const addCustomerBtn = document.querySelector('[data-bs-target="#addCustomerModal"]');
    if (addCustomerBtn) {
        addCustomerBtn.href = "{% url 'customer_create' %}";
        addCustomerBtn.removeAttribute('data-bs-target');
        addCustomerBtn.removeAttribute('data-bs-toggle');
    }
});
</script>
{% endblock %}
