from inventory.models import Service
import json

print("=== SERVICE CATEGORY ANALYSIS ===")

# Get all unique categories
categories = {}
for service in Service.objects.all():
    cat = service.category
    if cat not in categories:
        categories[cat] = []
    categories[cat].append({
        'name': service.name,
        'price': str(service.price_per_session)
    })

print(f"Total services: {Service.objects.count()}")
print(f"Categories found: {list(categories.keys())}")

for cat, services in categories.items():
    print(f"\n{cat}: {len(services)} services")
    for service in services[:3]:  # Show first 3 services in each category
        print(f"  - {service['name']}: P{service['price']}")
    if len(services) > 3:
        print(f"  ... and {len(services) - 3} more")

print("\n=== CATEGORY MAPPING CHECK ===")
# Check what categories are expected vs actual
expected_categories = ['facial_treatment', 'gluta_treatment', 'ipl_treatment', 'aesthetic_procedure', 'skincare', 'other']
actual_categories = list(categories.keys())

print(f"Expected: {expected_categories}")
print(f"Actual: {actual_categories}")

missing = set(expected_categories) - set(actual_categories)
extra = set(actual_categories) - set(expected_categories)

if missing:
    print(f"Missing categories: {missing}")
if extra:
    print(f"Extra categories: {extra}")
