from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q, Sum, Count, F
from django.db import models
from django.core.paginator import Paginator
from django.utils import timezone
from datetime import timedel<PERSON>

from .models import Customer
from .models_unified import UnifiedTransaction, UnifiedTransactionLineItem
from .forms_customer import CustomerForm, CustomerSearchForm


@login_required
def customer_list(request):
    """List all customers with search and filtering"""
    form = CustomerSearchForm(request.GET)
    customers = Customer.objects.all()
    
    # Apply search filters
    if form.is_valid():
        search = form.cleaned_data.get('search')
        has_transactions = form.cleaned_data.get('has_transactions')
        date_from = form.cleaned_data.get('date_from')
        date_to = form.cleaned_data.get('date_to')
        
        if search:
            customers = customers.filter(
                Q(name__icontains=search) |
                Q(phone__icontains=search) |
                Q(email__icontains=search)
            )
        
        if has_transactions == 'yes':
            # Customers with transactions
            customer_names = UnifiedTransaction.objects.values_list('customer_name', flat=True).distinct()
            customers = customers.filter(name__in=customer_names)
        elif has_transactions == 'no':
            # Customers without transactions
            customer_names = UnifiedTransaction.objects.values_list('customer_name', flat=True).distinct()
            customers = customers.exclude(name__in=customer_names)
        
        if date_from:
            customers = customers.filter(created_at__date__gte=date_from)
        
        if date_to:
            customers = customers.filter(created_at__date__lte=date_to)
    
    # Order customers
    customers = customers.order_by('name')
    
    # Pagination
    paginator = Paginator(customers, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistics
    stats = {
        'total_customers': Customer.objects.count(),
        'customers_with_transactions': Customer.objects.filter(
            name__in=UnifiedTransaction.objects.values_list('customer_name', flat=True).distinct()
        ).count(),
        'new_customers_this_month': Customer.objects.filter(
            created_at__month=timezone.now().month,
            created_at__year=timezone.now().year
        ).count(),
    }
    
    context = {
        'page_obj': page_obj,
        'form': form,
        'stats': stats,
        'page_title': 'Customer Management'
    }
    return render(request, 'customers/customer_list.html', context)


@login_required
def customer_detail(request, pk):
    """View customer details and transaction history"""
    customer = get_object_or_404(Customer, pk=pk)
    
    # Get customer's transactions
    transactions = UnifiedTransaction.objects.filter(
        customer_name=customer.name
    ).order_by('-payment_date')
    
    # Get transaction statistics
    transaction_stats = {
        'total_transactions': transactions.count(),
        'total_spent': transactions.aggregate(total=Sum('total_amount'))['total'] or 0,
        'last_transaction': transactions.first(),
        'avg_transaction': transactions.aggregate(avg=Sum('total_amount'))['avg'] or 0
    }
    
    if transaction_stats['total_transactions'] > 0:
        transaction_stats['avg_transaction'] = transaction_stats['total_spent'] / transaction_stats['total_transactions']
    
    # Get service and product breakdown
    service_items = UnifiedTransactionLineItem.objects.filter(
        transaction__customer_name=customer.name,
        item_type='service'
    ).select_related('service')
    
    product_items = UnifiedTransactionLineItem.objects.filter(
        transaction__customer_name=customer.name,
        item_type='product'
    ).select_related('product')
    
    # Pagination for transactions
    paginator = Paginator(transactions, 10)
    page_number = request.GET.get('page')
    transactions_page = paginator.get_page(page_number)
    
    context = {
        'customer': customer,
        'transactions_page': transactions_page,
        'transaction_stats': transaction_stats,
        'service_items': service_items,
        'product_items': product_items,
        'page_title': f'Customer: {customer.name}'
    }
    return render(request, 'customers/customer_detail.html', context)


@login_required
def customer_create(request):
    """Create a new customer"""
    if request.method == 'POST':
        form = CustomerForm(request.POST)
        if form.is_valid():
            customer = form.save()
            
            # Record activity
            from user_management.views import record_user_activity
            record_user_activity(
                user=request.user,
                action='OTHER',
                description=f'Created new customer: {customer.name}',
                request=request
            )
            
            messages.success(request, f'Customer "{customer.name}" created successfully!')
            return redirect('customer_detail', pk=customer.pk)
    else:
        form = CustomerForm()
    
    context = {
        'form': form,
        'page_title': 'Add New Customer'
    }
    return render(request, 'customers/customer_form.html', context)


@login_required
def customer_edit(request, pk):
    """Edit an existing customer"""
    customer = get_object_or_404(Customer, pk=pk)
    
    if request.method == 'POST':
        form = CustomerForm(request.POST, instance=customer)
        if form.is_valid():
            # Track changes for audit
            original_name = customer.name
            original_phone = customer.phone
            original_email = customer.email
            
            updated_customer = form.save()
            
            # Record changes
            changes = []
            if original_name != updated_customer.name:
                changes.append(f"Name: '{original_name}' → '{updated_customer.name}'")
            if original_phone != updated_customer.phone:
                changes.append(f"Phone: '{original_phone}' → '{updated_customer.phone}'")
            if original_email != updated_customer.email:
                changes.append(f"Email: '{original_email}' → '{updated_customer.email}'")
            
            if changes:
                from user_management.views import record_user_activity
                record_user_activity(
                    user=request.user,
                    action='OTHER',
                    description=f'Updated customer {customer.name}: {"; ".join(changes)}',
                    request=request
                )
            
            messages.success(request, f'Customer "{customer.name}" updated successfully!')
            return redirect('customer_detail', pk=customer.pk)
    else:
        form = CustomerForm(instance=customer)
    
    context = {
        'form': form,
        'customer': customer,
        'page_title': f'Edit Customer: {customer.name}'
    }
    return render(request, 'customers/customer_form.html', context)


@login_required
def customer_history(request, pk):
    """View customer edit history and activity log"""
    customer = get_object_or_404(Customer, pk=pk)
    
    # Get user activities related to this customer
    from user_management.models import UserActivity
    activities = UserActivity.objects.filter(
        description__icontains=customer.name
    ).order_by('-timestamp')
    
    context = {
        'customer': customer,
        'activities': activities,
        'page_title': f'Customer History: {customer.name}'
    }
    return render(request, 'customers/customer_history.html', context)


@login_required
def customer_delete(request, pk):
    """Delete a customer (soft delete by marking inactive)"""
    customer = get_object_or_404(Customer, pk=pk)
    
    if request.method == 'POST':
        # Check if customer has transactions
        transaction_count = UnifiedTransaction.objects.filter(customer_name=customer.name).count()
        
        if transaction_count > 0:
            messages.warning(
                request, 
                f'Cannot delete customer "{customer.name}" because they have {transaction_count} transaction(s). '
                'Customer data is preserved for transaction history.'
            )
        else:
            customer_name = customer.name
            customer.delete()
            
            # Record activity
            from user_management.views import record_user_activity
            record_user_activity(
                user=request.user,
                action='OTHER',
                description=f'Deleted customer: {customer_name}',
                request=request
            )
            
            messages.success(request, f'Customer "{customer_name}" deleted successfully!')
        
        return redirect('customer_list')
    
    return redirect('customer_detail', pk=pk)
