from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.utils import timezone
from django.db.models import Q, Count, Sum, F
from datetime import datetime, timedelta
import json

from .models_unified import UnifiedTransaction, UnifiedTransactionLineItem
from .models_session import SessionConsumption, SessionAppointment


@login_required
def customer_sessions_dashboard(request):
    """Dashboard showing all customers with active sessions"""
    
    # Get all line items with remaining sessions
    active_sessions = UnifiedTransactionLineItem.objects.filter(
        item_type='service',
        sessions__gt=0
    ).select_related('transaction', 'service').annotate(
        sessions_remaining_calc=F('sessions') - F('sessions_used')
    ).filter(sessions_remaining_calc__gt=0)
    
    # Group by customer
    customers_with_sessions = {}
    for line_item in active_sessions:
        customer_name = line_item.transaction.customer_name
        customer_phone = line_item.transaction.customer_phone
        
        if customer_name not in customers_with_sessions:
            customers_with_sessions[customer_name] = {
                'customer_name': customer_name,
                'customer_phone': customer_phone,
                'services': [],
                'total_sessions_remaining': 0
            }
        
        customers_with_sessions[customer_name]['services'].append({
            'line_item': line_item,
            'service_name': line_item.service.name,
            'sessions_remaining': line_item.sessions_remaining,
            'transaction_id': line_item.transaction.transaction_id,
            'purchase_date': line_item.transaction.payment_date
        })
        customers_with_sessions[customer_name]['total_sessions_remaining'] += line_item.sessions_remaining
    
    context = {
        'customers_with_sessions': customers_with_sessions.values(),
        'total_customers': len(customers_with_sessions),
        'total_sessions_remaining': sum(
            customer['total_sessions_remaining'] 
            for customer in customers_with_sessions.values()
        )
    }
    
    return render(request, 'sessions/customer_sessions_dashboard.html', context)


@login_required
def customer_session_detail(request, line_item_id):
    """Detailed view of a customer's specific service sessions"""
    
    line_item = get_object_or_404(
        UnifiedTransactionLineItem.objects.select_related('transaction', 'service'),
        id=line_item_id,
        item_type='service'
    )
    
    # Get session consumptions
    session_consumptions = SessionConsumption.objects.filter(
        line_item=line_item
    ).order_by('session_number')
    
    # Get upcoming appointments
    upcoming_appointments = SessionAppointment.objects.filter(
        line_item=line_item,
        status__in=['scheduled', 'confirmed']
    ).order_by('appointment_date')
    
    context = {
        'line_item': line_item,
        'transaction': line_item.transaction,
        'service': line_item.service,
        'session_consumptions': session_consumptions,
        'upcoming_appointments': upcoming_appointments,
        'sessions_remaining': line_item.sessions_remaining,
        'next_session_number': line_item.sessions_used + 1
    }
    
    return render(request, 'sessions/customer_session_detail.html', context)


@login_required
@require_POST
def consume_session(request, line_item_id):
    """Consume a session for a customer"""
    
    line_item = get_object_or_404(
        UnifiedTransactionLineItem,
        id=line_item_id,
        item_type='service'
    )
    
    if line_item.sessions_remaining <= 0:
        return JsonResponse({
            'success': False,
            'error': 'No remaining sessions available'
        })
    
    try:
        data = json.loads(request.body)
        
        # Create session consumption
        session_consumption = SessionConsumption.objects.create(
            line_item=line_item,
            session_number=line_item.sessions_used + 1,
            session_date=timezone.now(),
            staff_performed_by=data.get('staff_performed_by', ''),
            duration_minutes=data.get('duration_minutes'),
            notes=data.get('notes', ''),
            customer_feedback=data.get('customer_feedback', ''),
            status='completed'
        )
        
        return JsonResponse({
            'success': True,
            'message': f'Session {session_consumption.session_number} completed successfully',
            'sessions_remaining': line_item.sessions_remaining,
            'session_consumption_id': session_consumption.id
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_POST
def schedule_appointment(request, line_item_id):
    """Schedule an appointment for a session"""
    
    line_item = get_object_or_404(
        UnifiedTransactionLineItem,
        id=line_item_id,
        item_type='service'
    )
    
    if line_item.sessions_remaining <= 0:
        return JsonResponse({
            'success': False,
            'error': 'No remaining sessions available'
        })
    
    try:
        data = json.loads(request.body)
        
        appointment_date = datetime.fromisoformat(data.get('appointment_date'))
        session_number = line_item.sessions_used + 1
        
        # Check if appointment already exists for this session
        existing_appointment = SessionAppointment.objects.filter(
            line_item=line_item,
            session_number=session_number
        ).first()
        
        if existing_appointment:
            return JsonResponse({
                'success': False,
                'error': f'Appointment already exists for session {session_number}'
            })
        
        # Create appointment
        appointment = SessionAppointment.objects.create(
            line_item=line_item,
            appointment_date=appointment_date,
            session_number=session_number,
            assigned_staff=data.get('assigned_staff', ''),
            appointment_notes=data.get('appointment_notes', ''),
            status='scheduled'
        )
        
        return JsonResponse({
            'success': True,
            'message': f'Appointment scheduled for session {session_number}',
            'appointment_id': appointment.id,
            'appointment_date': appointment.appointment_date.isoformat()
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
def session_calendar(request):
    """Calendar view of all scheduled appointments"""

    # Get date range (default to current month)
    today = timezone.now().date()
    start_date = today.replace(day=1)
    if today.month == 12:
        end_date = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
    else:
        end_date = today.replace(month=today.month + 1, day=1) - timedelta(days=1)

    # Get appointments for the month
    appointments = SessionAppointment.objects.filter(
        appointment_date__date__range=[start_date, end_date]
    ).select_related('line_item__transaction', 'line_item__service').order_by('appointment_date')

    # Group appointments by date
    appointments_by_date = {}
    for appointment in appointments:
        date_key = appointment.appointment_date.date()
        if date_key not in appointments_by_date:
            appointments_by_date[date_key] = []
        appointments_by_date[date_key].append(appointment)

    # Calculate summary stats
    total_appointments = appointments.count()
    confirmed_appointments = appointments.filter(status='confirmed').count()
    scheduled_appointments = appointments.filter(status='scheduled').count()
    overdue_appointments = appointments.filter(
        appointment_date__lt=timezone.now(),
        status__in=['scheduled', 'confirmed']
    ).count()

    context = {
        'appointments_by_date': appointments_by_date,
        'current_month': today.strftime('%B %Y'),
        'start_date': start_date,
        'end_date': end_date,
        'total_appointments': total_appointments,
        'confirmed_appointments': confirmed_appointments,
        'scheduled_appointments': scheduled_appointments,
        'overdue_appointments': overdue_appointments,
    }

    return render(request, 'sessions/session_calendar.html', context)


@login_required
def session_reports(request):
    """Session consumption and appointment reports"""
    
    # Get date range from request
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)  # Last 30 days
    
    if request.GET.get('start_date'):
        start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
    if request.GET.get('end_date'):
        end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
    
    # Session consumption stats
    session_consumptions = SessionConsumption.objects.filter(
        session_date__date__range=[start_date, end_date]
    ).select_related('line_item__service')
    
    # Group by service
    service_stats = {}
    for consumption in session_consumptions:
        service_name = consumption.service_name
        if service_name not in service_stats:
            service_stats[service_name] = {
                'service_name': service_name,
                'sessions_completed': 0,
                'total_duration': 0,
                'customers': set()
            }
        
        service_stats[service_name]['sessions_completed'] += 1
        if consumption.duration_minutes:
            service_stats[service_name]['total_duration'] += consumption.duration_minutes
        service_stats[service_name]['customers'].add(consumption.customer_name)
    
    # Convert sets to counts
    for stats in service_stats.values():
        stats['unique_customers'] = len(stats['customers'])
        stats['avg_duration'] = (
            stats['total_duration'] / stats['sessions_completed'] 
            if stats['sessions_completed'] > 0 else 0
        )
        del stats['customers']  # Remove set object for template
    
    context = {
        'service_stats': service_stats.values(),
        'start_date': start_date,
        'end_date': end_date,
        'total_sessions_completed': sum(stats['sessions_completed'] for stats in service_stats.values()),
        'date_range_days': (end_date - start_date).days + 1
    }
    
    return render(request, 'sessions/session_reports.html', context)
