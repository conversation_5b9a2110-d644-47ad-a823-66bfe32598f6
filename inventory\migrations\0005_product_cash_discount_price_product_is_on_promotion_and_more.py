# Generated by Django 5.1.6 on 2025-07-06 13:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0004_discountvoucher_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='cash_discount_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Special cash payment price', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='is_on_promotion',
            field=models.BooleanField(default=False, help_text='Is this product currently on promotion?'),
        ),
        migrations.AddField(
            model_name='product',
            name='promotion_end_date',
            field=models.DateTimeField(blank=True, help_text='Promotion end date', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='promotion_start_date',
            field=models.DateTimeField(blank=True, help_text='Promotion start date', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='promotional_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Special promotional price', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='service',
            name='cash_discount_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Special cash payment price', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='service',
            name='is_on_promotion',
            field=models.BooleanField(default=False, help_text='Is this service currently on promotion?'),
        ),
        migrations.AddField(
            model_name='service',
            name='promotion_end_date',
            field=models.DateTimeField(blank=True, help_text='Promotion end date', null=True),
        ),
        migrations.AddField(
            model_name='service',
            name='promotion_start_date',
            field=models.DateTimeField(blank=True, help_text='Promotion start date', null=True),
        ),
        migrations.AddField(
            model_name='service',
            name='promotional_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Special promotional price', max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='selling_price',
            field=models.DecimalField(decimal_places=2, help_text='Regular selling price per unit', max_digits=10),
        ),
        migrations.AlterField(
            model_name='service',
            name='price_per_session',
            field=models.DecimalField(decimal_places=2, help_text='Regular price per individual session', max_digits=10),
        ),
    ]
