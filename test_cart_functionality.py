#!/usr/bin/env python3
"""
Test cart functionality
Run with: python manage.py shell < test_cart_functionality.py
"""

from django.test import RequestFactory
from django.contrib.sessions.middleware import SessionMiddleware
from inventory.models import Service, Product
from inventory.utils.cart import ShoppingCart

def test_cart_functionality():
    """Test the shopping cart functionality"""
    print("=== TESTING CART FUNCTIONALITY ===")
    
    # Create a mock request with session
    factory = RequestFactory()
    request = factory.get('/')
    
    # Add session middleware
    middleware = SessionMiddleware(lambda x: None)
    middleware.process_request(request)
    request.session.save()
    
    # Initialize cart
    cart = ShoppingCart(request)
    print(f"Initial cart state: Empty = {cart.is_empty()}")
    
    # Test adding a service
    service = Service.objects.first()
    if service:
        sessions_added = cart.add_service(service, 2)
        print(f"Added service: {service.name} x{sessions_added} sessions")
        print(f"Cart items count: {cart.get_items_count()}")
        print(f"Total items: {cart.get_total_items()}")
        print(f"Subtotal: P{cart.get_subtotal()}")
    
    # Test adding a product
    product = Product.objects.filter(current_stock__gt=0).first()
    if product:
        quantity_added = cart.add_product(product, 1)
        print(f"Added product: {product.name} x{quantity_added}")
        print(f"Cart items count: {cart.get_items_count()}")
        print(f"Total items: {cart.get_total_items()}")
        print(f"Subtotal: P{cart.get_subtotal()}")
    
    # Test cart items
    items = cart.get_items()
    print(f"\nCart contents:")
    for item in items:
        print(f"  - {item['name']}: {item.get('quantity', item.get('sessions', 0))} x P{item['unit_price']} = P{item['total']}")
    
    # Test cart dictionary
    cart_dict = cart.to_dict()
    print(f"\nCart dictionary:")
    print(f"  Items count: {cart_dict['items_count']}")
    print(f"  Total quantity: {cart_dict['total_quantity']}")
    print(f"  Subtotal: P{cart_dict['subtotal']}")
    print(f"  Is empty: {cart_dict['is_empty']}")
    
    # Test removing items
    if service:
        cart.remove_service(service.id)
        print(f"\nRemoved service. Items count: {cart.get_items_count()}")
    
    if product:
        cart.remove_product(product.id)
        print(f"Removed product. Items count: {cart.get_items_count()}")
    
    print(f"Final cart state: Empty = {cart.is_empty()}")
    
    return True

def test_cart_methods():
    """Test all cart methods exist and work"""
    print("\n=== TESTING CART METHODS ===")
    
    factory = RequestFactory()
    request = factory.get('/')
    middleware = SessionMiddleware(lambda x: None)
    middleware.process_request(request)
    request.session.save()
    
    cart = ShoppingCart(request)
    
    # Test all methods exist
    methods_to_test = [
        'get_items', 'get_total_items', 'get_subtotal', 'get_items_count',
        'get_total_quantity', 'is_empty', 'to_dict', 'clear'
    ]
    
    for method_name in methods_to_test:
        if hasattr(cart, method_name):
            try:
                method = getattr(cart, method_name)
                result = method()
                print(f"✓ {method_name}(): {type(result).__name__}")
            except Exception as e:
                print(f"✗ {method_name}(): Error - {e}")
        else:
            print(f"✗ {method_name}: Method not found")
    
    return True

def test_service_categories():
    """Test service categories and counts"""
    print("\n=== TESTING SERVICE CATEGORIES ===")
    
    categories = {}
    for service in Service.objects.filter(is_active=True):
        cat = service.category
        if cat not in categories:
            categories[cat] = []
        categories[cat].append(service.name)
    
    print("Service categories and counts:")
    for cat, services in categories.items():
        print(f"  {cat}: {len(services)} services")
        # Show first 3 services as examples
        for service_name in services[:3]:
            print(f"    - {service_name}")
        if len(services) > 3:
            print(f"    ... and {len(services) - 3} more")
    
    return len(categories) > 0

if __name__ == "__main__":
    try:
        print("🧪 CART FUNCTIONALITY TEST")
        print("=" * 50)
        
        # Test 1: Basic cart functionality
        cart_test = test_cart_functionality()
        
        # Test 2: Cart methods
        methods_test = test_cart_methods()
        
        # Test 3: Service categories
        categories_test = test_service_categories()
        
        # Summary
        print("\n" + "=" * 50)
        print("TEST SUMMARY")
        print("=" * 50)
        print(f"Cart functionality: {'PASS' if cart_test else 'FAIL'}")
        print(f"Cart methods: {'PASS' if methods_test else 'FAIL'}")
        print(f"Service categories: {'PASS' if categories_test else 'FAIL'}")
        
        all_pass = cart_test and methods_test and categories_test
        print(f"\nOVERALL: {'ALL TESTS PASS' if all_pass else 'SOME TESTS FAILED'}")
        
        if all_pass:
            print("\nCart system is working correctly!")
            print("The POS system should now be fully functional.")
        else:
            print("\nSome issues detected. Please check the test results above.")
            
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
