# Generated by Django 5.1.6 on 2025-07-06 16:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0007_unifiedtransaction_attachment_1_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='attachment_1',
            field=models.FileField(blank=True, help_text='Customer document (ID, medical record, etc.)', null=True, upload_to='customer_files/'),
        ),
        migrations.AddField(
            model_name='customer',
            name='attachment_1_description',
            field=models.CharField(blank=True, help_text='Description of attachment 1', max_length=255),
        ),
        migrations.AddField(
            model_name='customer',
            name='attachment_2',
            field=models.FileField(blank=True, help_text='Additional customer document', null=True, upload_to='customer_files/'),
        ),
        migrations.AddField(
            model_name='customer',
            name='attachment_2_description',
            field=models.CharField(blank=True, help_text='Description of attachment 2', max_length=255),
        ),
        migrations.AddField(
            model_name='customer',
            name='attachment_3',
            field=models.FileField(blank=True, help_text='Additional customer document', null=True, upload_to='customer_files/'),
        ),
        migrations.AddField(
            model_name='customer',
            name='attachment_3_description',
            field=models.CharField(blank=True, help_text='Description of attachment 3', max_length=255),
        ),
    ]
