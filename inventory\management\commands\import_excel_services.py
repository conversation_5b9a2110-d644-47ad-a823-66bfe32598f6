from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
import json
import os

from inventory.models import Service, Product


class Command(BaseCommand):
    help = 'Import services and products from extracted Excel data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--json-file',
            type=str,
            default='extracted_services.json',
            help='Path to the extracted services JSON file'
        )
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing services before importing'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be imported without actually importing'
        )

    def handle(self, *args, **options):
        json_file = options['json_file']
        clear_existing = options['clear_existing']
        dry_run = options['dry_run']

        if not os.path.exists(json_file):
            self.stdout.write(
                self.style.ERROR(f'JSON file "{json_file}" not found!')
            )
            return

        # Load the extracted data
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                services_data = json.load(f)
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error reading JSON file: {e}')
            )
            return

        if not services_data:
            self.stdout.write(
                self.style.ERROR('No services data found in JSON file')
            )
            return

        self.stdout.write(f'Found {len(services_data)} services to import')

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No data will be imported'))

        # Clear existing data if requested
        if clear_existing and not dry_run:
            self.stdout.write(self.style.WARNING('Clearing existing services...'))
            Service.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing services cleared'))

        # Import services
        services_created = 0
        services_updated = 0
        products_created = 0

        for service_data in services_data:
            try:
                # Determine if this should be a service or product
                is_product = self.is_product_item(service_data)
                
                if is_product:
                    # Import as product
                    if not dry_run:
                        created = self.create_product(service_data)
                        if created:
                            products_created += 1
                    else:
                        self.stdout.write(f'Would create product: {service_data["name"]}')
                        products_created += 1
                else:
                    # Import as service
                    if not dry_run:
                        service, created = self.create_service(service_data)
                        if created:
                            services_created += 1
                            self.stdout.write(f'Created service: {service.name}')
                        else:
                            services_updated += 1
                            self.stdout.write(f'Updated service: {service.name}')
                    else:
                        self.stdout.write(f'Would create service: {service_data["name"]}')
                        services_created += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error importing {service_data.get("name", "Unknown")}: {e}')
                )

        # Summary
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f'DRY RUN COMPLETE:\n'
                    f'- Would create {services_created} services\n'
                    f'- Would create {products_created} products'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Import completed:\n'
                    f'- Created {services_created} services\n'
                    f'- Updated {services_updated} services\n'
                    f'- Created {products_created} products'
                )
            )

    def is_product_item(self, service_data):
        """Determine if an item should be imported as a product rather than service"""
        name = service_data.get('name', '').lower()
        category = service_data.get('category', '').lower()
        sheet_source = service_data.get('sheet_source', '').lower()
        
        # Items from "TAKE HOME MEDS" sheet are products
        if 'take home meds' in sheet_source:
            return True
        
        # Items with product-like names
        product_keywords = [
            'serum', 'cream', 'lotion', 'mask', 'set', 'kit', 
            'supplement', 'vitamin', 'capsule', 'tablet', 'bottle'
        ]
        
        if any(keyword in name for keyword in product_keywords):
            return True
        
        # Skincare category items are usually products
        if category == 'skincare':
            return True
            
        return False

    def create_service(self, service_data):
        """Create or update a service"""
        # Map the extracted data to Service model fields
        service_fields = {
            'name': service_data['name'],
            'category': service_data.get('category', 'other'),
            'description': service_data.get('description', ''),
            'price_per_session': Decimal(str(service_data.get('selling_price', 0))),
            'duration_minutes': 60,  # Default duration
            'is_active': service_data.get('is_active', True)
        }

        # Create or update the service
        service, created = Service.objects.get_or_create(
            name=service_fields['name'],
            defaults=service_fields
        )

        if not created:
            # Update existing service
            for field, value in service_fields.items():
                setattr(service, field, value)
            service.save()

        return service, created

    def create_product(self, service_data):
        """Create a product from service data"""
        # Generate unique SKU
        sku = service_data.get('sku', f"PROD{Product.objects.count() + 1:04d}")
        
        # Ensure SKU is unique
        counter = 1
        original_sku = sku
        while Product.objects.filter(sku=sku).exists():
            sku = f"{original_sku}_{counter}"
            counter += 1

        # Map category to product categories
        category_mapping = {
            'skincare': 'skincare',
            'facial_treatment': 'skincare',
            'gluta_treatment': 'supplements',
            'aesthetic_procedure': 'equipment',
            'other': 'other'
        }
        
        product_category = category_mapping.get(
            service_data.get('category', 'other'), 
            'other'
        )

        # Create product
        product_fields = {
            'name': service_data['name'],
            'brand': 'REMR Aesthetic Centre',  # Default brand
            'category': product_category,
            'description': service_data.get('description', ''),
            'sku': sku,
            'unit': service_data.get('unit', 'piece'),
            'current_stock': service_data.get('current_stock', 0),
            'minimum_stock': service_data.get('minimum_stock', 5),
            'maximum_stock': service_data.get('maximum_stock', 100),
            'cost_price': Decimal(str(service_data.get('cost_price', 0))),
            'selling_price': Decimal(str(service_data.get('selling_price', 0))),
            'is_active': service_data.get('is_active', True)
        }

        product, created = Product.objects.get_or_create(
            sku=sku,
            defaults=product_fields
        )

        return created
