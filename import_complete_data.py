#!/usr/bin/env python3
"""
Complete data import script based on data.py
Run with: python manage.py shell < import_complete_data.py
"""

from inventory.models import Service, Product
from decimal import Decimal
import re

def clean_price(price_str):
    """Clean price string and convert to Decimal"""
    if not price_str:
        return Decimal('0.00')
    
    # Remove 'P' prefix and any whitespace
    price_str = str(price_str).replace('P', '').replace('₱', '').strip()
    
    # Handle empty or invalid prices
    if not price_str or price_str == '':
        return Decimal('0.00')
    
    try:
        return Decimal(price_str)
    except:
        return Decimal('0.00')

def import_facial_services():
    """Import facial services and add-on masks"""
    print("=== IMPORTING FACIAL SERVICES ===")
    
    facial_services = [
        {"name": "Deep Cleansing facial", "price": "499", "inventory": "Hydrating mask 1 pc"},
        {"name": "Acne control facial", "price": "999", "inventory": "Acne mask 1 pc"},
        {"name": "Stemcell infusion", "price": "999", "inventory": "1 Hydrating mask, 1 vial of stemcell serum (8ml)"},
        {"name": "Ageless facial", "price": "999", "inventory": "Collagen mask 1 pc"},
        {"name": "K- bb & K- blush", "price": "1499", "inventory": "1 Hydrating mask, 1 vials of BB serum & 1ml of serum blush"},
        {"name": "Intensive Acne cure", "price": "1999", "inventory": "1 acne mask"},
        {"name": "Ultimate Anti aging", "price": "1999", "inventory": "1 collagen mask"},
        {"name": "Vampire facial", "price": "3499", "inventory": "1 VIT C MASK"},
        
        # Add-on masks
        {"name": "Hydrating mask", "price": "199", "inventory": "1 Hydrating mask"},
        {"name": "Gold Collagen mask", "price": "249", "inventory": "1 Gold Collagen mask"},
        {"name": "Seaweed mask", "price": "249", "inventory": "1 seaweed mask"},
        {"name": "VIT E mask", "price": "199", "inventory": "1 VIT E mask"},
        {"name": "Hyaluronic mask", "price": "199", "inventory": "1 Hyaluronic mask"},
        {"name": "Nicotinamide", "price": "199", "inventory": "1 Nicotinamide"},
        {"name": "Vitamin C mask", "price": "199", "inventory": "1 Vitamin C mask"},
        {"name": "Acne mask sheet", "price": "200", "inventory": "1 Acne mask sheet"},
    ]
    
    for service_data in facial_services:
        service, created = Service.objects.get_or_create(
            name=service_data["name"],
            defaults={
                'category': 'facial_treatment',
                'price_per_session': clean_price(service_data["price"]),
                'description': f'Inventory required: {service_data["inventory"]}',
                'duration_minutes': 60,
                'is_active': True
            }
        )
        if created:
            print(f"Created: {service.name} - ₱{service.price_per_session}")
        else:
            print(f"Exists: {service.name}")

def import_gluta_services():
    """Import Gluta IV treatments"""
    print("\n=== IMPORTING GLUTA SERVICES ===")
    
    gluta_services = [
        {"name": "Classic skin white IV Push", "price": "499", "inventory": "1 vial puregold neo, 1 Vit C/ pnss 10ml"},
        {"name": "CSW drip", "price": "999", "inventory": "1 Egf / 1 vit c/ 1 pnss 50ml/ 1 macroset"},
        {"name": "Wellness push", "price": "999", "inventory": "1 tatio/ 1 vitamin c/ 1 pnss 10ml"},
        {"name": "Signature IV Push", "price": "999", "inventory": "1 vial puregold neo, 1 Vit C, 1 ampoule collagen"},
        {"name": "Blooming pink/Beauty drip", "price": "999", "inventory": "1 vial GS, 1 50ML pnss, 1 macroset"},
        {"name": "Tone and firm", "price": "999", "inventory": "1 ampoule Vitamin B12, 1 50ML pnss, 1 macroset, 1 ampoule Lcar"},
        {"name": "Bye melasma", "price": "999", "inventory": "1 vial puregold, 1 50ML pnss, 1 macroset, 1 ampoule traminex"},
        {"name": "Recovery Hydration", "price": "1499", "inventory": "1 50ML pnss, 1 macroset, 2 ampoule vit c, 1 ampoule Vitamin B12, 1 ampoule collagen"},
        {"name": "Slim beauty blend", "price": "1499", "inventory": "1 vial glutax gs, 1 50ML pnss, 1 ampoule Lcar, 1 macroset"},
        {"name": "Ultra white infusion", "price": "1999", "inventory": "1 vial glutax advance, 1 50ML pnss, 1 macroset"},
        {"name": "Oriskin Signature cocktail", "price": "2499", "inventory": "1 infinite trio, 1 100ML pnss, 1 macroset"},
        {"name": "TM FOR MORENA", "price": "2000", "inventory": "1 100ML 1 TM PNSS 1 MACROSET"},
        
        # Add-ons
        {"name": "VIT C", "price": "299", "inventory": "1 ampoule of vit c"},
        {"name": "VIT B", "price": "299", "inventory": "1 ampoule of vit B"},
        {"name": "Collagen", "price": "499", "inventory": "1 ampoule of collagen"},
        {"name": "Placenta", "price": "499", "inventory": "1 ampoule of Placenta"},
        {"name": "Traminex", "price": "499", "inventory": "1 ampoule of traminex"},
        {"name": "LCAR", "price": "499", "inventory": "1 ampoule of Lcar"},
        {"name": "Stemcell IM", "price": "999", "inventory": "1 ampoule of stemcell IM"},
        {"name": "Laros pink", "price": "999", "inventory": "1 laros pink"},
        {"name": "Bioskin stem cell", "price": "850", "inventory": "1 Bioskin stemcell"},
    ]
    
    for service_data in gluta_services:
        service, created = Service.objects.get_or_create(
            name=service_data["name"],
            defaults={
                'category': 'gluta_treatment',
                'price_per_session': clean_price(service_data["price"]),
                'description': f'Inventory required: {service_data["inventory"]}',
                'duration_minutes': 45,
                'is_active': True
            }
        )
        if created:
            print(f"Created: {service.name} - ₱{service.price_per_session}")
        else:
            print(f"Exists: {service.name}")

def import_meso_services():
    """Import Mesotherapy services"""
    print("\n=== IMPORTING MESO SERVICES ===")
    
    meso_services = [
        {"name": "Meso Vline face & chin (mild)", "price": "1499", "inventory": "2 ml of VLINE"},
        {"name": "Meso Vline face & chin (mod to sev)", "price": "1999", "inventory": "4 ml of VLINE"},
        {"name": "Meso arms/tummy/thigh (mild)", "price": "1999", "inventory": "6 ml of kabelline"},
        {"name": "Meso arms/tummy/thigh (mod to sev)", "price": "2499", "inventory": "8 ml of kabelline"},
        {"name": "KABELLINE PER ML", "price": "300", "inventory": "1ML OF KABELLINE"},
        {"name": "MESO PER ML", "price": "300", "inventory": "1ML OF MESOLIPO"},
    ]
    
    for service_data in meso_services:
        service, created = Service.objects.get_or_create(
            name=service_data["name"],
            defaults={
                'category': 'aesthetic_procedure',
                'price_per_session': clean_price(service_data["price"]),
                'description': f'Inventory required: {service_data["inventory"]}',
                'duration_minutes': 60,
                'is_active': True
            }
        )
        if created:
            print(f"Created: {service.name} - ₱{service.price_per_session}")
        else:
            print(f"Exists: {service.name}")

def import_opt_services():
    """Import OPT/IPL services"""
    print("\n=== IMPORTING OPT/IPL SERVICES ===")

    opt_services = [
        {"name": "Acne control laser", "price": "999", "inventory": "None"},
        {"name": "Melasma laser", "price": "999", "inventory": "None"},
        {"name": "Back acne laser", "price": "2499", "inventory": "None"},
        {"name": "Butt acne laser", "price": "1999", "inventory": "None"},
        {"name": "Vascular laser", "price": "1499", "inventory": "None"},
        {"name": "UA hair removal", "price": "799", "inventory": "None"},
        {"name": "UA whitening", "price": "799", "inventory": "None"},
        {"name": "UA hair removal w/ whitening", "price": "999", "inventory": "None"},
        {"name": "Bikini hair removal", "price": "1499", "inventory": "None"},
        {"name": "Bikini whitening", "price": "1499", "inventory": "None"},
        {"name": "Bikini Hair removal w/ whitening", "price": "1999", "inventory": "None"},
        {"name": "Half arms Hair removal", "price": "1999", "inventory": "None"},
        {"name": "Full arms Hair removal", "price": "2999", "inventory": "None"},
        {"name": "Half legs Hair removal", "price": "1999", "inventory": "None"},
        {"name": "Full legs Hair removal", "price": "2999", "inventory": "None"},
        {"name": "UA color corrector mask", "price": "249", "inventory": "1 UA color corrector mask"},
        {"name": "HAIR REMOVAL FULL FACE", "price": "1499", "inventory": "None"},
        {"name": "BEARD", "price": "799", "inventory": "None"},
        {"name": "MUSTACHE", "price": "499", "inventory": "None"},
        {"name": "VASCULAR THERAPY", "price": "999", "inventory": "None"},
    ]

    for service_data in opt_services:
        service, created = Service.objects.get_or_create(
            name=service_data["name"],
            defaults={
                'category': 'ipl_treatment',
                'price_per_session': clean_price(service_data["price"]),
                'description': f'Inventory required: {service_data["inventory"]}',
                'duration_minutes': 45,
                'is_active': True
            }
        )
        if created:
            print(f"Created: {service.name} - ₱{service.price_per_session}")
        else:
            print(f"Exists: {service.name}")

def import_rf_services():
    """Import RF services"""
    print("\n=== IMPORTING RF SERVICES ===")

    rf_services = [
        {"name": "RF (face & eyes)", "price": "899", "inventory": "None"},
        {"name": "RF (arms, tummy, thigh)", "price": "999", "inventory": "None"},
    ]

    for service_data in rf_services:
        service, created = Service.objects.get_or_create(
            name=service_data["name"],
            defaults={
                'category': 'aesthetic_procedure',
                'price_per_session': clean_price(service_data["price"]),
                'description': f'Inventory required: {service_data["inventory"]}',
                'duration_minutes': 60,
                'is_active': True
            }
        )
        if created:
            print(f"Created: {service.name} - ₱{service.price_per_session}")
        else:
            print(f"Exists: {service.name}")

def import_hifu_warts_services():
    """Import HIFU and Warts services"""
    print("\n=== IMPORTING HIFU & WARTS SERVICES ===")

    hifu_warts_services = [
        {"name": "HIFU (FACE/HALF SHOTS)", "price": "5000", "inventory": "None"},
        {"name": "HIFU (FACE/FULL SHOTS)", "price": "10000", "inventory": "None"},
        {"name": "HIFU (ARMS/TUMMY/THIGH/HALF SHOTS)", "price": "8000", "inventory": "None"},
        {"name": "HIFU (ARMS/TUMMY/THIGH/FULL SHOTS)", "price": "15000", "inventory": "None"},
        {"name": "WARTS/PER AREA", "price": "499", "inventory": "None"},
        {"name": "SKIN TAG PER PC (SMALL)", "price": "100", "inventory": "None"},
        {"name": "SKIN TAG PER PC (BIG MIN)", "price": "200", "inventory": "None"},
        {"name": "MILIA", "price": "100", "inventory": "None"},
        {"name": "SYRINGOMA PER AREA", "price": "999", "inventory": "None"},
    ]

    for service_data in hifu_warts_services:
        service, created = Service.objects.get_or_create(
            name=service_data["name"],
            defaults={
                'category': 'aesthetic_procedure',
                'price_per_session': clean_price(service_data["price"]),
                'description': f'Inventory required: {service_data["inventory"]}',
                'duration_minutes': 90 if 'HIFU' in service_data["name"] else 30,
                'is_active': True
            }
        )
        if created:
            print(f"Created: {service.name} - ₱{service.price_per_session}")
        else:
            print(f"Exists: {service.name}")

def import_carbon_laser_services():
    """Import Carbon Laser services"""
    print("\n=== IMPORTING CARBON LASER SERVICES ===")

    carbon_services = [
        {"name": "Black doll face", "price": "1699", "inventory": "1 Hydrating mask"},
        {"name": "Carbon other parts", "price": "999", "inventory": "None"},
        {"name": "Tattoo removal", "price": "1499", "inventory": "None"},
    ]

    for service_data in carbon_services:
        service, created = Service.objects.get_or_create(
            name=service_data["name"],
            defaults={
                'category': 'aesthetic_procedure',
                'price_per_session': clean_price(service_data["price"]),
                'description': f'Inventory required: {service_data["inventory"]}',
                'duration_minutes': 45,
                'is_active': True
            }
        )
        if created:
            print(f"Created: {service.name} - ₱{service.price_per_session}")
        else:
            print(f"Exists: {service.name}")

def import_invasive_services():
    """Import Invasive procedures"""
    print("\n=== IMPORTING INVASIVE SERVICES ===")

    invasive_services = [
        {"name": "Botox forehead", "price": "5000", "inventory": "Botox 4ml (14 units)"},
        {"name": "Botox Crowfeet", "price": "5000", "inventory": "Botox 4ml (12 units)"},
        {"name": "Alartox", "price": "5000", "inventory": "Botox 4ml (12 units)"},
        {"name": "Sweatox", "price": "5000", "inventory": "Botox 4ml (50 units)"},
        {"name": "Glabellar", "price": "3499", "inventory": "Botox 4ml (8 units)"},
        {"name": "Botox per unit", "price": "199", "inventory": "Botox 4ml (1 unit)"},
        {"name": "Mesonanomax", "price": "5000", "inventory": "2 hyaron 2 hyaluronic mask"},
        {"name": "Pimple injection", "price": "199", "inventory": "0.01 ml"},
        {"name": "Keloid injection", "price": "999", "inventory": "0.01ml"},
        {"name": "Sole/Handtox", "price": "9999", "inventory": "Botox 4ml (50 units)"},
        {"name": "Hair growth therapy", "price": "9999", "inventory": "None"},
    ]

    for service_data in invasive_services:
        service, created = Service.objects.get_or_create(
            name=service_data["name"],
            defaults={
                'category': 'aesthetic_procedure',
                'price_per_session': clean_price(service_data["price"]),
                'description': f'Inventory required: {service_data["inventory"]}',
                'duration_minutes': 30,
                'is_active': True
            }
        )
        if created:
            print(f"Created: {service.name} - ₱{service.price_per_session}")
        else:
            print(f"Exists: {service.name}")

def import_take_home_products():
    """Import Take Home products"""
    print("\n=== IMPORTING TAKE HOME PRODUCTS ===")

    products = [
        {"name": "Revitalizing set", "price": "650", "category": "skincare"},
        {"name": "Acne set", "price": "750", "category": "skincare"},
        {"name": "UA kit", "price": "550", "category": "skincare"},
        {"name": "Bikini kit", "price": "550", "category": "skincare"},
        {"name": "Gluta gummy", "price": "1150", "category": "supplements"},
        {"name": "Gluta berry set", "price": "650", "category": "supplements"},
        {"name": "Anti bacterial cream", "price": "200", "category": "skincare"},
        {"name": "Sunblock cream", "price": "200", "category": "skincare"},
        {"name": "VIP CARD", "price": "750", "category": "other"},
        {"name": "Vit C Serum", "price": "350", "category": "skincare"},
        {"name": "Hya Serum", "price": "350", "category": "skincare"},
        {"name": "Aloe soap", "price": "350", "category": "skincare"},
        {"name": "Nia soap", "price": "350", "category": "skincare"},
        {"name": "Oatmeal soap", "price": "350", "category": "skincare"},
        {"name": "CLYNDA TONER", "price": "350", "category": "skincare"},
    ]

    for product_data in products:
        product, created = Product.objects.get_or_create(
            name=product_data["name"],
            defaults={
                'category': product_data["category"],
                'selling_price': clean_price(product_data["price"]),
                'cost_price': clean_price(product_data["price"]) * Decimal('0.6'),  # Assume 40% markup
                'current_stock': 50,  # Default stock
                'minimum_stock': 10,
                'unit': 'piece',
                'is_active': True,
                'description': f'Take home product - {product_data["name"]}'
            }
        )
        if created:
            print(f"Created: {product.name} - ₱{product.selling_price}")
        else:
            print(f"Exists: {product.name}")

# Run the import functions
if __name__ == "__main__":
    print("Starting complete data import...")
    import_facial_services()
    import_gluta_services()
    import_meso_services()
    import_opt_services()
    import_rf_services()
    import_hifu_warts_services()
    import_carbon_laser_services()
    import_invasive_services()
    import_take_home_products()

    # Print final summary
    print("\n=== IMPORT SUMMARY ===")
    print(f"Total Services: {Service.objects.count()}")
    print(f"Total Products: {Product.objects.count()}")

    # Category breakdown
    print("\nService Categories:")
    categories = {}
    for service in Service.objects.all():
        cat = service.category
        if cat not in categories:
            categories[cat] = 0
        categories[cat] += 1

    for cat, count in categories.items():
        print(f"  {cat}: {count} services")

    print("\nImport completed!")
