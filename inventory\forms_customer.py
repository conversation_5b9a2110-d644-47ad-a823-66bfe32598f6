from django import forms
from django.core.exceptions import ValidationError
from .models import Customer


class CustomerForm(forms.ModelForm):
    """Form for creating and editing customers"""
    
    class Meta:
        model = Customer
        fields = [
            'name', 'phone', 'email', 'address',
            'date_of_birth', 'emergency_contact', 'medical_notes',
            'attachment_1', 'attachment_1_description',
            'attachment_2', 'attachment_2_description',
            'attachment_3', 'attachment_3_description'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Customer full name',
                'required': True
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+63 XXX XXX XXXX'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Customer address...'
            }),
            'date_of_birth': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'emergency_contact': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Emergency contact name and phone'
            }),
            'medical_notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Medical notes, allergies, special instructions...'
            }),
            # Attachment widgets
            'attachment_1': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*,.pdf,.doc,.docx'
            }),
            'attachment_1_description': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Description (e.g., ID copy, medical record, etc.)'
            }),
            'attachment_2': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*,.pdf,.doc,.docx'
            }),
            'attachment_2_description': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Description (e.g., Insurance card, etc.)'
            }),
            'attachment_3': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*,.pdf,.doc,.docx'
            }),
            'attachment_3_description': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Description (e.g., Medical certificate, etc.)'
            }),
        }
        labels = {
            'name': 'Customer Name',
            'phone': 'Phone Number',
            'email': 'Email Address',
            'address': 'Address',
            'date_of_birth': 'Date of Birth',
            'emergency_contact': 'Emergency Contact',
            'medical_notes': 'Medical Notes'
        }

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if not name or len(name.strip()) < 2:
            raise ValidationError('Customer name must be at least 2 characters long.')
        return name.strip()

    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if phone:
            # Remove all non-digit characters
            phone_digits = ''.join(filter(str.isdigit, phone))
            if len(phone_digits) < 10:
                raise ValidationError('Phone number must contain at least 10 digits.')
        return phone

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            # Check if email already exists for another customer
            existing_customer = Customer.objects.filter(email=email).exclude(pk=self.instance.pk if self.instance else None)
            if existing_customer.exists():
                raise ValidationError('A customer with this email already exists.')
        return email


class CustomerSearchForm(forms.Form):
    """Form for searching customers"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by name, phone, or email...'
        })
    )
    
    has_transactions = forms.ChoiceField(
        required=False,
        choices=[
            ('', 'All Customers'),
            ('yes', 'With Transactions'),
            ('no', 'Without Transactions')
        ],
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='Created From'
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='Created To'
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise ValidationError('Start date must be before end date.')
        
        return cleaned_data
