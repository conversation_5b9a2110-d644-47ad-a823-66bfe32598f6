from django.db import models
from django.utils import timezone
from decimal import Decimal
from django.core.validators import MinValueValidator, MaxValueValidator
from .models import Service, Product
import uuid


class UnifiedTransaction(models.Model):
    """Unified transaction model that can handle both products and services"""
    PAYMENT_MODE_CHOICES = [
        ('cash', 'Cash'),
        ('gcash', 'GCash')
    ]
    
    PAYMENT_STATUS_CHOICES = [
        ('paid', 'Fully Paid'),
        ('partial', 'Partially Paid'),
        ('pending', 'Pending Payment')
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded')
    ]
    
    # Transaction identification
    transaction_id = models.CharField(max_length=20, unique=True, help_text="Unique transaction ID")
    
    # Customer information
    customer_name = models.CharField(max_length=255, help_text="Customer's full name")
    customer_phone = models.<PERSON><PERSON><PERSON><PERSON>(max_length=20, blank=True, help_text="Customer's phone number")
    customer_email = models.EmailField(blank=True, help_text="Customer's email address")
    
    # Transaction totals
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, help_text="Subtotal before discount")
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text="Manual discount percentage (0-10%)")
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Total discount amount (manual + voucher)")
    voucher_discount = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text="Discount from vouchers")
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, help_text="Final total amount")

    # Voucher information
    voucher_codes_used = models.TextField(blank=True, help_text="Comma-separated list of voucher codes used")
    
    # Payment information
    payment_mode = models.CharField(max_length=15, choices=PAYMENT_MODE_CHOICES, default='cash')
    payment_status = models.CharField(max_length=10, choices=PAYMENT_STATUS_CHOICES, default='paid')
    payment_date = models.DateTimeField(default=timezone.now, help_text="Date and time of payment")
    
    # Payment verification fields
    cash_amount_received = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Amount received in cash")
    gcash_reference_number = models.CharField(max_length=15, blank=True, help_text="GCash reference number")
    
    # Transaction status and metadata
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='active')
    remarks = models.TextField(blank=True, help_text="Additional notes or comments")
    staff_received_by = models.CharField(max_length=255, blank=True, help_text="Staff member who processed transaction")
    
    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-payment_date']
        verbose_name = "Unified Transaction"
        verbose_name_plural = "Unified Transactions"
    
    def __str__(self):
        return f"{self.transaction_id} - {self.customer_name} - ₱{self.total_amount}"
    
    @property
    def change_amount(self):
        """Calculate change for cash payments"""
        if self.payment_mode == 'cash' and self.cash_amount_received:
            return max(0, self.cash_amount_received - self.total_amount)
        return 0
    
    @property
    def line_items_count(self):
        """Get total number of line items"""
        return self.line_items.count()
    
    def save(self, *args, **kwargs):
        """Override save to generate transaction ID and calculate totals"""
        if not self.transaction_id:
            # Generate unique transaction ID
            prefix = 'UT'
            last_transaction = UnifiedTransaction.objects.filter(
                transaction_id__startswith=prefix
            ).order_by('-transaction_id').first()

            if last_transaction:
                last_number = int(last_transaction.transaction_id[2:])
                new_number = last_number + 1
            else:
                new_number = 1

            self.transaction_id = f"{prefix}{new_number:06d}"
        
        # Calculate manual discount amount
        manual_discount = Decimal('0.00')
        if self.discount_percent > 0:
            manual_discount = self.subtotal * (self.discount_percent / 100)

        # Total discount = manual discount + voucher discount
        self.discount_amount = manual_discount + self.voucher_discount

        # Calculate total amount
        self.total_amount = self.subtotal - self.discount_amount
        
        super().save(*args, **kwargs)


class UnifiedTransactionLineItem(models.Model):
    """Line items for unified transactions - can be products or services"""
    ITEM_TYPE_CHOICES = [
        ('product', 'Product'),
        ('service', 'Service')
    ]
    
    transaction = models.ForeignKey(UnifiedTransaction, on_delete=models.CASCADE, related_name='line_items')
    item_type = models.CharField(max_length=10, choices=ITEM_TYPE_CHOICES)
    
    # Product fields (used when item_type='product')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, null=True, blank=True)
    quantity = models.IntegerField(null=True, blank=True, help_text="Quantity for products")
    
    # Service fields (used when item_type='service')
    service = models.ForeignKey(Service, on_delete=models.CASCADE, null=True, blank=True)
    sessions = models.IntegerField(null=True, blank=True, help_text="Number of sessions for services")
    sessions_used = models.IntegerField(default=0, help_text="Sessions already consumed")
    
    # Common fields
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Price per unit/session at time of transaction")
    line_total = models.DecimalField(max_digits=10, decimal_places=2, help_text="Total for this line item")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['id']
        verbose_name = "Transaction Line Item"
        verbose_name_plural = "Transaction Line Items"
    
    def __str__(self):
        if self.item_type == 'product':
            return f"{self.product.name} x{self.quantity} - ₱{self.line_total}"
        else:
            return f"{self.service.name} x{self.sessions} sessions - ₱{self.line_total}"
    
    @property
    def item_name(self):
        """Get the name of the item (product or service)"""
        if self.item_type == 'product':
            return self.product.name if self.product else 'Unknown Product'
        else:
            return self.service.name if self.service else 'Unknown Service'
    
    @property
    def sessions_remaining(self):
        """Calculate remaining sessions for services"""
        if self.item_type == 'service' and self.sessions:
            return max(0, self.sessions - self.sessions_used)
        return 0
    
    def save(self, *args, **kwargs):
        """Override save to calculate line total and update stock"""
        # Calculate line total
        if self.item_type == 'product' and self.quantity:
            self.line_total = self.unit_price * self.quantity
            
            # Update product stock if this is a new line item
            if self.pk is None and self.product:
                self.product.current_stock -= self.quantity
                self.product.save()
                
        elif self.item_type == 'service' and self.sessions:
            self.line_total = self.unit_price * self.sessions
        
        super().save(*args, **kwargs)
        
        # Update transaction subtotal
        if self.transaction:
            self.transaction.subtotal = sum(
                item.line_total for item in self.transaction.line_items.all()
            )
            self.transaction.save()
    
    def clean(self):
        """Validate line item data"""
        from django.core.exceptions import ValidationError
        
        if self.item_type == 'product':
            if not self.product:
                raise ValidationError("Product is required for product line items")
            if not self.quantity or self.quantity <= 0:
                raise ValidationError("Quantity must be greater than 0 for products")
            # Check stock availability
            if self.product and self.quantity > self.product.current_stock:
                raise ValidationError(f"Insufficient stock. Available: {self.product.current_stock}")
                
        elif self.item_type == 'service':
            if not self.service:
                raise ValidationError("Service is required for service line items")
            if not self.sessions or self.sessions <= 0:
                raise ValidationError("Sessions must be greater than 0 for services")


class DiscountVoucher(models.Model):
    """Voucher system for multiple discount types"""
    VOUCHER_TYPE_CHOICES = [
        ('percentage', 'Percentage Discount'),
        ('fixed_amount', 'Fixed Amount Discount'),
        ('promotional', 'Promotional Price'),
        ('cash_discount', 'Cash Payment Discount'),
        ('loyalty', 'Loyalty Discount'),
        ('seasonal', 'Seasonal Discount'),
        ('bulk', 'Bulk Purchase Discount'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('expired', 'Expired'),
        ('used_up', 'Used Up'),
    ]

    # Voucher identification
    code = models.CharField(max_length=20, unique=True, help_text="Unique voucher code")
    name = models.CharField(max_length=100, help_text="Voucher name/description")
    voucher_type = models.CharField(max_length=15, choices=VOUCHER_TYPE_CHOICES)

    # Discount values
    discount_percentage = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Percentage discount (0-100%)"
    )
    discount_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        help_text="Fixed discount amount"
    )
    promotional_price = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        help_text="Special promotional price"
    )

    # Usage limits
    max_uses = models.IntegerField(default=1, help_text="Maximum number of times this voucher can be used")
    current_uses = models.IntegerField(default=0, help_text="Current number of uses")
    max_uses_per_customer = models.IntegerField(default=1, help_text="Max uses per customer")

    # Validity period
    valid_from = models.DateTimeField(help_text="Voucher valid from this date")
    valid_until = models.DateTimeField(help_text="Voucher valid until this date")

    # Conditions
    minimum_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default=0,
        help_text="Minimum transaction amount to use this voucher"
    )
    applicable_to_services = models.BooleanField(default=True, help_text="Can be applied to services")
    applicable_to_products = models.BooleanField(default=True, help_text="Can be applied to products")

    # Status and metadata
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='active')
    created_by = models.CharField(max_length=100, blank=True, help_text="Staff who created the voucher")
    remarks = models.TextField(blank=True, help_text="Additional notes")

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Discount Voucher"
        verbose_name_plural = "Discount Vouchers"

    def __str__(self):
        return f"{self.code} - {self.name}"

    @property
    def is_valid(self):
        """Check if voucher is currently valid"""
        now = timezone.now()
        return (
            self.status == 'active' and
            self.valid_from <= now <= self.valid_until and
            self.current_uses < self.max_uses
        )

    @property
    def uses_remaining(self):
        """Get remaining uses for this voucher"""
        return max(0, self.max_uses - self.current_uses)

    def calculate_discount(self, subtotal):
        """Calculate discount amount for given subtotal"""
        if not self.is_valid or subtotal < self.minimum_amount:
            return Decimal('0.00')

        if self.voucher_type == 'percentage' and self.discount_percentage:
            return subtotal * (self.discount_percentage / 100)
        elif self.voucher_type == 'fixed_amount' and self.discount_amount:
            return min(self.discount_amount, subtotal)  # Don't exceed subtotal
        elif self.voucher_type == 'promotional' and self.promotional_price:
            return max(Decimal('0.00'), subtotal - self.promotional_price)

        return Decimal('0.00')

    def use_voucher(self):
        """Mark voucher as used (increment usage count)"""
        if self.current_uses < self.max_uses:
            self.current_uses += 1
            if self.current_uses >= self.max_uses:
                self.status = 'used_up'
            self.save()
            return True
        return False


class VoucherUsage(models.Model):
    """Track voucher usage per transaction"""
    voucher = models.ForeignKey(DiscountVoucher, on_delete=models.CASCADE, related_name='usages')
    transaction = models.ForeignKey(UnifiedTransaction, on_delete=models.CASCADE, related_name='voucher_usages')
    customer_phone = models.CharField(max_length=20, blank=True, help_text="Customer phone for tracking per-customer usage")
    discount_applied = models.DecimalField(max_digits=10, decimal_places=2, help_text="Actual discount amount applied")
    used_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-used_at']
        verbose_name = "Voucher Usage"
        verbose_name_plural = "Voucher Usages"

    def __str__(self):
        return f"{self.voucher.code} used in {self.transaction.transaction_id} - ₱{self.discount_applied}"
