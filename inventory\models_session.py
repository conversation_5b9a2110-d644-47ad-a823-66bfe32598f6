from django.db import models
from django.utils import timezone
from .models_unified import UnifiedTransactionLineItem

class SessionConsumption(models.Model):
    """Track individual session consumption for unified transactions"""
    
    line_item = models.ForeignKey(
        UnifiedTransactionLineItem, 
        on_delete=models.CASCADE, 
        related_name='session_consumptions',
        help_text="The service line item this session belongs to"
    )
    
    # Session details
    session_number = models.IntegerField(help_text="Session number (1, 2, 3, etc.)")
    session_date = models.DateTimeField(help_text="When the session was performed")
    
    # Staff and service details
    staff_performed_by = models.CharField(
        max_length=255, 
        blank=True, 
        help_text="Staff member who performed the service"
    )
    duration_minutes = models.IntegerField(
        null=True, 
        blank=True, 
        help_text="Actual duration of the session"
    )
    
    # Session notes and observations
    notes = models.TextField(blank=True, help_text="Session notes, observations, or comments")
    customer_feedback = models.TextField(blank=True, help_text="Customer feedback or comments")
    
    # Session status
    STATUS_CHOICES = [
        ('completed', 'Completed'),
        ('no_show', 'No Show'),
        ('cancelled', 'Cancelled'),
        ('rescheduled', 'Rescheduled'),
    ]
    status = models.Char<PERSON>ield(max_length=12, choices=STATUS_CHOICES, default='completed')
    
    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['session_number']
        unique_together = ['line_item', 'session_number']
        verbose_name = "Session Consumption"
        verbose_name_plural = "Session Consumptions"
    
    def __str__(self):
        return f"{self.line_item.service.name} - Session {self.session_number} ({self.line_item.transaction.customer_name})"
    
    def save(self, *args, **kwargs):
        """Auto-increment sessions_used in the parent line item"""
        is_new = self.pk is None
        
        super().save(*args, **kwargs)
        
        # Update sessions_used count in the line item
        if is_new and self.status == 'completed':
            self.line_item.sessions_used = self.line_item.session_consumptions.filter(
                status='completed'
            ).count()
            self.line_item.save()
    
    @property
    def customer_name(self):
        """Get customer name from the transaction"""
        return self.line_item.transaction.customer_name
    
    @property
    def service_name(self):
        """Get service name"""
        return self.line_item.service.name if self.line_item.service else 'Unknown Service'


class SessionAppointment(models.Model):
    """Track upcoming appointments for purchased sessions"""
    
    line_item = models.ForeignKey(
        UnifiedTransactionLineItem,
        on_delete=models.CASCADE,
        related_name='appointments',
        help_text="The service line item this appointment is for"
    )
    
    # Appointment details
    appointment_date = models.DateTimeField(help_text="Scheduled appointment date and time")
    session_number = models.IntegerField(help_text="Which session number this appointment is for")
    
    # Staff assignment
    assigned_staff = models.CharField(
        max_length=255,
        blank=True,
        help_text="Staff member assigned to perform the service"
    )
    
    # Appointment status
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('confirmed', 'Confirmed'),
        ('completed', 'Completed'),
        ('no_show', 'No Show'),
        ('cancelled', 'Cancelled'),
        ('rescheduled', 'Rescheduled'),
    ]
    status = models.CharField(max_length=12, choices=STATUS_CHOICES, default='scheduled')
    
    # Notes
    appointment_notes = models.TextField(blank=True, help_text="Appointment notes or special instructions")
    cancellation_reason = models.TextField(blank=True, help_text="Reason for cancellation if applicable")
    
    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['appointment_date']
        unique_together = ['line_item', 'session_number']
        verbose_name = "Session Appointment"
        verbose_name_plural = "Session Appointments"
    
    def __str__(self):
        return f"{self.service_name} - Session {self.session_number} ({self.appointment_date.strftime('%Y-%m-%d %H:%M')})"
    
    @property
    def customer_name(self):
        """Get customer name from the transaction"""
        return self.line_item.transaction.customer_name
    
    @property
    def service_name(self):
        """Get service name"""
        return self.line_item.service.name if self.line_item.service else 'Unknown Service'
    
    @property
    def is_overdue(self):
        """Check if appointment is overdue"""
        return self.appointment_date < timezone.now() and self.status in ['scheduled', 'confirmed']
    
    def complete_session(self, staff_performed_by=None, notes='', customer_feedback='', duration_minutes=None):
        """Complete the session and create a session consumption record"""
        if self.status != 'completed':
            # Create session consumption record
            SessionConsumption.objects.create(
                line_item=self.line_item,
                session_number=self.session_number,
                session_date=timezone.now(),
                staff_performed_by=staff_performed_by or self.assigned_staff,
                duration_minutes=duration_minutes,
                notes=notes,
                customer_feedback=customer_feedback,
                status='completed'
            )
            
            # Update appointment status
            self.status = 'completed'
            self.save()
            
            return True
        return False
