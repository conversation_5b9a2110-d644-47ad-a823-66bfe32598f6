from decimal import Decimal
from django.conf import settings
from ..models import Product, Service


class ShoppingCart:
    """Shopping cart utility class for managing cart items in session"""
    
    def __init__(self, request):
        """Initialize the cart"""
        self.session = request.session
        cart = self.session.get(settings.CART_SESSION_ID)
        if not cart:
            # Save an empty cart in the session
            cart = self.session[settings.CART_SESSION_ID] = {}
        self.cart = cart
    
    def add_product(self, product, quantity=1, override_quantity=False):
        """Add a product to the cart or update its quantity"""
        product_id = str(product.id)
        item_key = f"product_{product_id}"
        
        if item_key not in self.cart:
            self.cart[item_key] = {
                'item_type': 'product',
                'product_id': product.id,
                'quantity': 0,
                'unit_price': str(product.selling_price),
                'name': product.name,
                'unit': product.unit
            }
        
        if override_quantity:
            self.cart[item_key]['quantity'] = quantity
        else:
            self.cart[item_key]['quantity'] += quantity
        
        # Check stock availability
        if self.cart[item_key]['quantity'] > product.current_stock:
            self.cart[item_key]['quantity'] = product.current_stock
            
        self.save()
        return self.cart[item_key]['quantity']
    
    def add_service(self, service, sessions=1, override_sessions=False):
        """Add a service to the cart or update its sessions"""
        service_id = str(service.id)
        item_key = f"service_{service_id}"
        
        if item_key not in self.cart:
            self.cart[item_key] = {
                'item_type': 'service',
                'service_id': service.id,
                'sessions': 0,
                'unit_price': str(service.price_per_session),
                'name': service.name,
                'duration_minutes': service.duration_minutes
            }
        
        if override_sessions:
            self.cart[item_key]['sessions'] = sessions
        else:
            self.cart[item_key]['sessions'] += sessions
            
        self.save()
        return self.cart[item_key]['sessions']
    
    def remove_item(self, item_key):
        """Remove an item from the cart"""
        if item_key in self.cart:
            del self.cart[item_key]
            self.save()
    
    def update_item_quantity(self, item_key, quantity):
        """Update the quantity/sessions of an item"""
        if item_key in self.cart:
            item = self.cart[item_key]
            
            if item['item_type'] == 'product':
                # Check stock availability
                product = Product.objects.get(id=item['product_id'])
                if quantity > product.current_stock:
                    quantity = product.current_stock
                item['quantity'] = quantity
                
            elif item['item_type'] == 'service':
                item['sessions'] = quantity
            
            if quantity <= 0:
                self.remove_item(item_key)
            else:
                self.save()
    
    def get_item_total(self, item_key):
        """Get the total price for a specific item"""
        if item_key not in self.cart:
            return Decimal('0.00')
        
        item = self.cart[item_key]
        unit_price = Decimal(item['unit_price'])
        
        if item['item_type'] == 'product':
            return unit_price * item['quantity']
        else:
            return unit_price * item['sessions']
    
    def get_subtotal(self):
        """Calculate the subtotal of all items in the cart"""
        subtotal = Decimal('0.00')
        for item_key in self.cart:
            subtotal += self.get_item_total(item_key)
        return subtotal
    
    def get_total_with_discount(self, discount_percent=0):
        """Calculate total with discount applied"""
        subtotal = self.get_subtotal()
        discount_amount = subtotal * (Decimal(str(discount_percent)) / 100)
        return subtotal - discount_amount
    
    def get_discount_amount(self, discount_percent=0):
        """Calculate discount amount"""
        subtotal = self.get_subtotal()
        return subtotal * (Decimal(str(discount_percent)) / 100)
    
    def get_items(self):
        """Get all items in the cart with calculated totals"""
        items = []
        for item_key, item_data in self.cart.items():
            item = item_data.copy()
            item['key'] = item_key
            item['total'] = self.get_item_total(item_key)
            
            # Add additional product/service data
            if item['item_type'] == 'product':
                try:
                    product = Product.objects.get(id=item['product_id'])
                    item['current_stock'] = product.current_stock
                    item['stock_status'] = product.stock_status
                except Product.DoesNotExist:
                    # Remove invalid item
                    self.remove_item(item_key)
                    continue
                    
            elif item['item_type'] == 'service':
                try:
                    service = Service.objects.get(id=item['service_id'])
                    item['category'] = service.get_category_display()
                except Service.DoesNotExist:
                    # Remove invalid item
                    self.remove_item(item_key)
                    continue
            
            items.append(item)
        
        return items
    
    def get_items_count(self):
        """Get total number of items in cart"""
        return len(self.cart)
    
    def get_total_quantity(self):
        """Get total quantity/sessions of all items"""
        total = 0
        for item in self.cart.values():
            if item['item_type'] == 'product':
                total += item['quantity']
            else:
                total += item['sessions']
        return total

    def get_total_items(self):
        """Alias for get_total_quantity for compatibility"""
        return self.get_total_quantity()

    def remove_product(self, product_id):
        """Remove a product from cart by product ID"""
        item_key = f"product_{product_id}"
        self.remove_item(item_key)

    def remove_service(self, service_id):
        """Remove a service from cart by service ID"""
        item_key = f"service_{service_id}"
        self.remove_item(item_key)
    
    def clear(self):
        """Clear the cart"""
        del self.session[settings.CART_SESSION_ID]
        self.save()
    
    def save(self):
        """Mark the session as modified to make sure it gets saved"""
        self.session.modified = True
    
    def is_empty(self):
        """Check if cart is empty"""
        return len(self.cart) == 0
    
    def validate_stock(self):
        """Validate that all products in cart have sufficient stock"""
        errors = []
        for item_key, item in self.cart.items():
            if item['item_type'] == 'product':
                try:
                    product = Product.objects.get(id=item['product_id'])
                    if item['quantity'] > product.current_stock:
                        errors.append({
                            'item_key': item_key,
                            'item_name': item['name'],
                            'requested': item['quantity'],
                            'available': product.current_stock
                        })
                except Product.DoesNotExist:
                    errors.append({
                        'item_key': item_key,
                        'item_name': item['name'],
                        'error': 'Product no longer exists'
                    })
        return errors
    
    def to_dict(self):
        """Convert cart to dictionary for JSON serialization"""
        return {
            'items': self.get_items(),
            'subtotal': float(self.get_subtotal()),
            'items_count': self.get_items_count(),
            'total_quantity': self.get_total_quantity(),
            'is_empty': self.is_empty()
        }
