{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold text-primary mb-0">
                <i class="fas fa-cash-register me-2"></i>{{ page_title }}
            </h2>
            <p class="text-muted mb-0">Comprehensive analytics for REMR Aesthetic Centre POS system</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Services
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_services }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-spa fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Products
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_products }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Transactions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_transactions }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-receipt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Monthly Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₱{{ stats.month_revenue|floatformat:2|intcomma }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-peso-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Cards -->
    <div class="row mb-4">
        <div class="col-md-4 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Today's Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₱{{ stats.today_revenue|floatformat:2|intcomma }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Weekly Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₱{{ stats.week_revenue|floatformat:2|intcomma }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-week fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Monthly Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₱{{ stats.month_revenue|floatformat:2|intcomma }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Row -->
    <div class="row mb-4">
        <!-- Service Categories -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Service Categories</h6>
                    <i class="fas fa-spa"></i>
                </div>
                <div class="card-body">
                    {% if service_category_stats %}
                        {% for category in service_category_stats %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="font-weight-bold">{{ category.category|title|default:"Other" }}</div>
                                <div class="text-xs text-gray-500">{{ category.count }} service{{ category.count|pluralize }}</div>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-gray-500">₱{{ category.total_revenue|default:0|floatformat:2|intcomma }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-spa fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Service Data</h6>
                            <p class="text-muted small">Service analytics will appear here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Product Categories -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-success">Product Categories</h6>
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="card-body">
                    {% if product_category_stats %}
                        {% for category in product_category_stats %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="font-weight-bold">{{ category.category|title|default:"Other" }}</div>
                                <div class="text-xs text-gray-500">{{ category.count }} product{{ category.count|pluralize }}</div>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-gray-500">₱{{ category.total_revenue|default:0|floatformat:2|intcomma }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Product Data</h6>
                            <p class="text-muted small">Product analytics will appear here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Popular Items Row -->
    <div class="row mb-4">
        <!-- Popular Services -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Popular Services</h6>
                    <i class="fas fa-star"></i>
                </div>
                <div class="card-body">
                    {% if popular_services %}
                        {% for service in popular_services %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="font-weight-bold">{{ service.name }}</div>
                                <div class="text-xs text-gray-500">{{ service.transaction_count }} transaction{{ service.transaction_count|pluralize }}</div>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-gray-500">₱{{ service.price_per_session }}/session</div>
                                <div class="text-xs text-success">₱{{ service.total_revenue|default:0|floatformat:2|intcomma }} total</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-spa fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Popular Services</h6>
                            <p class="text-muted small">Popular services will appear here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Popular Products -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-success">Popular Products</h6>
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="card-body">
                    {% if popular_products %}
                        {% for product in popular_products %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="font-weight-bold">{{ product.name }}</div>
                                <div class="text-xs text-gray-500">{{ product.transaction_count }} sale{{ product.transaction_count|pluralize }}</div>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-gray-500">₱{{ product.selling_price }}/{{ product.unit }}</div>
                                <div class="text-xs text-success">₱{{ product.total_revenue|default:0|floatformat:2|intcomma }} total</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Popular Products</h6>
                            <p class="text-muted small">Popular products will appear here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-500 {
    color: #858796 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}
