{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary mb-0">
                    <i class="fas fa-users me-2"></i>{{ page_title }}
                </h2>
                <a href="{% url 'customer_create' %}" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>Add New Customer
                </a>
            </div>
            <p class="text-muted mb-0">Manage customer information and transaction history</p>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                    <h4 class="text-primary">{{ stats.total_customers }}</h4>
                    <p class="text-muted mb-0">Total Customers</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="fas fa-shopping-cart fa-2x text-success mb-2"></i>
                    <h4 class="text-success">{{ stats.customers_with_transactions }}</h4>
                    <p class="text-muted mb-0">Active Customers</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="fas fa-user-plus fa-2x text-info mb-2"></i>
                    <h4 class="text-info">{{ stats.new_customers_this_month }}</h4>
                    <p class="text-muted mb-0">New This Month</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-search me-2"></i>Search & Filter</h5>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            {{ form.search.label_tag }}
                            {{ form.search }}
                        </div>
                        <div class="col-md-2">
                            {{ form.has_transactions.label_tag }}
                            {{ form.has_transactions }}
                        </div>
                        <div class="col-md-2">
                            {{ form.date_from.label_tag }}
                            {{ form.date_from }}
                        </div>
                        <div class="col-md-2">
                            {{ form.date_to.label_tag }}
                            {{ form.date_to }}
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>Search
                            </button>
                            <a href="{% url 'customer_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer List -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>Customers</h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Name</th>
                                        <th>Contact</th>
                                        <th>Transactions</th>
                                        <th>Total Spent</th>
                                        <th>Last Visit</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for customer in page_obj %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    {{ customer.name|first|upper }}
                                                </div>
                                                <div>
                                                    <strong>{{ customer.name }}</strong>
                                                    {% if customer.date_of_birth %}
                                                        <br><small class="text-muted">DOB: {{ customer.date_of_birth|date:"M d, Y" }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if customer.phone %}
                                                <i class="fas fa-phone me-1"></i>{{ customer.phone }}<br>
                                            {% endif %}
                                            {% if customer.email %}
                                                <i class="fas fa-envelope me-1"></i>{{ customer.email }}
                                            {% endif %}
                                            {% if not customer.phone and not customer.email %}
                                                <span class="text-muted">No contact info</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% with transaction_count=customer.total_transactions %}
                                                {% if transaction_count > 0 %}
                                                    <span class="badge bg-success">{{ transaction_count }}</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">0</span>
                                                {% endif %}
                                            {% endwith %}
                                        </td>
                                        <td>
                                            {% with total_spent=customer.total_spent %}
                                                {% if total_spent > 0 %}
                                                    <span class="text-success fw-bold">₱{{ total_spent|floatformat:2 }}</span>
                                                {% else %}
                                                    <span class="text-muted">₱0.00</span>
                                                {% endif %}
                                            {% endwith %}
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ customer.updated_at|date:"M d, Y" }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'customer_detail' customer.pk %}" class="btn btn-sm btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'customer_edit' customer.pk %}" class="btn btn-sm btn-outline-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'customer_history' customer.pk %}" class="btn btn-sm btn-outline-info" title="History">
                                                    <i class="fas fa-history"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" title="Delete" 
                                                        onclick="confirmDelete('{{ customer.name }}', '{% url 'customer_delete' customer.pk %}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                        <nav aria-label="Customer pagination">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">First</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Previous</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Next</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Last</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Customers Found</h5>
                            <p class="text-muted">Start by adding your first customer.</p>
                            <a href="{% url 'customer_create' %}" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>Add First Customer
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 35px;
    height: 35px;
    font-size: 14px;
}
</style>

<script>
function confirmDelete(customerName, deleteUrl) {
    if (confirm(`Are you sure you want to delete customer "${customerName}"?\n\nThis action cannot be undone. If the customer has transactions, they will be preserved but no longer linked to a customer record.`)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;
        
        // Add CSRF token
        const csrfToken = document.querySelector('input[name=csrfmiddlewaretoken]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken.value;
            form.appendChild(csrfInput);
        }
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
