#!/usr/bin/env python3
"""
Script to check current prices in the system
Run with: python manage.py shell < check_prices.py
"""

from inventory.models import Service, Product

print('=== SERVICE PRICES BY CATEGORY ===')
categories = ['facial_treatment', 'gluta_treatment', 'ipl_treatment', 'aesthetic_procedure', 'skincare']
for cat in categories:
    services = Service.objects.filter(category=cat)[:5]
    if services:
        print(f'\n{cat.upper().replace("_", " ")}:')
        for service in services:
            print(f'  {service.name}: P{service.price_per_session}')

print('\n=== PRODUCT PRICES BY CATEGORY ===')
prod_categories = ['skincare', 'supplements', 'cosmetics', 'equipment']
for cat in prod_categories:
    products = Product.objects.filter(category=cat)[:5]
    if products:
        print(f'\n{cat.upper()}:')
        for product in products:
            print(f'  {product.name}: P{product.selling_price}')

print('\n=== TOTAL COUNTS ===')
print(f'Total Services: {Service.objects.count()}')
print(f'Total Products: {Product.objects.count()}')
